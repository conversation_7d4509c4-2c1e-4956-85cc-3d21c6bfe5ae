# 🚀 Custom ADK-Powered UI Implementation Plan

## 📋 **Project Overview**

**Goal**: Build a custom React/Vite frontend that directly leverages Google ADK capabilities for the BC Parent Activity Assistant, replacing the generic Vercel AI SDK implementation.

**Key Benefits**:
- 🛠️ **Tool Transparency**: Show real-time tool execution status
- 🎯 **Rich Activity Display**: Activity cards with registration, pricing, schedules
- 🧠 **Memory Visualization**: Show stored family preferences and history
- ⚡ **Performance**: Direct SSE streaming from ADK
- 🎨 **UX**: Purpose-built for BC activity searches

---

## 🏗️ **Implementation Phases**

### **Phase 1: Project Setup & Backend Foundation** ⏱️ 1-2 days

#### **1.1 Clean Project Structure**
```
agent_google/
├── backend/
│   ├── adk_server.py           # Enhanced ADK server
│   ├── multi_tool_agent/       # Agent implementation  
│   ├── requirements.txt        # Python dependencies
│   └── config.py              # Configuration
├── frontend/                   # NEW: Custom React/Vite UI
│   ├── src/
│   │   ├── components/        # React components
│   │   ├── hooks/             # Custom hooks
│   │   ├── services/          # API clients
│   │   ├── types/             # TypeScript definitions
│   │   └── utils/             # Utilities
│   ├── package.json           # Node dependencies
│   └── vite.config.ts         # Vite configuration
├── tests/                     # Test files
└── deprecated/                # Old Vercel UI files
```

#### **1.2 Enhanced Backend (adk_server.py)**
```python
# New endpoints to implement:
POST /api/chat_stream         # SSE chat endpoint  
GET  /api/session/{id}/status # Session health
POST /api/session/{id}/clear  # Clear session
GET  /api/tools/status        # Tool availability
POST /api/activity/export     # Calendar export
GET  /api/memory/summary      # Memory visualization
```

#### **1.3 SSE Event Types**
```typescript
interface ADKEvent {
  type: 'text_chunk' | 'tool_call_start' | 'tool_call_complete' | 
        'activity_card' | 'memory_update' | 'error' | 'turn_complete';
  data: any;
  timestamp: number;
  sessionId: string;
}
```

### **Phase 2: Core Frontend Architecture** ⏱️ 2-3 days

#### **2.1 React/Vite Setup**
```bash
# Commands to run:
npm create vite@latest frontend -- --template react-ts
cd frontend
npm install zustand @types/eventsource lucide-react tailwindcss
```

#### **2.2 State Management (Zustand)**
```typescript
interface AppState {
  // Session
  sessionId: string;
  isConnected: boolean;
  
  // Conversation  
  messages: Message[];
  isAgentTyping: boolean;
  currentTool: ToolStatus | null;
  
  // Memory
  childProfiles: Child[];
  activityPreferences: Preference[];
  
  // Activities
  activities: Activity[];
  registrations: Registration[];
  
  // UI
  sidebarOpen: boolean;
  activePanel: 'chat' | 'memory' | 'activities';
}
```

#### **2.3 Core Components**
- `ChatContainer.tsx` - Main chat interface
- `MessageList.tsx` - Conversation history
- `MessageInput.tsx` - User input with auto-suggestions
- `ToolStatus.tsx` - Real-time tool execution feedback
- `ActivityCard.tsx` - Rich activity display

### **Phase 3: SSE Streaming Implementation** ⏱️ 2-3 days

#### **3.1 Backend SSE Generator**
```python
async def custom_adk_sse_generator(session_id: str, messages: List[Dict]):
    """Enhanced SSE streaming with tool transparency"""
    
    # Send connection status
    yield sse_event("connection", {"session_id": session_id})
    
    async for adk_event in agent_run_stream:
        # Tool call detection
        if detect_tool_call(adk_event):
            tool_name = extract_tool_name(adk_event)
            yield sse_event("tool_call_start", {
                "tool": tool_name,
                "message": get_tool_message(tool_name)
            })
        
        # Agent text responses
        if is_agent_text(adk_event):
            yield sse_event("text_chunk", {
                "content": adk_event.content
            })
        
        # Activity data extraction
        if contains_activity_data(adk_event):
            activities = extract_activities(adk_event)
            yield sse_event("activity_card", {
                "activities": activities
            })
    
    yield sse_event("turn_complete", {})
```

#### **3.2 Frontend SSE Hook**
```typescript
export const useSSEConnection = (sessionId: string) => {
  const [events, setEvents] = useState<ADKEvent[]>([]);
  
  useEffect(() => {
    const eventSource = new EventSource(
      `/api/chat_stream?session_id=${sessionId}`
    );
    
    eventSource.onmessage = (event) => {
      const adkEvent = JSON.parse(event.data);
      handleADKEvent(adkEvent);
    };
    
    return () => eventSource.close();
  }, [sessionId]);
};
```

### **Phase 4: Rich UI Components** ⏱️ 3-4 days

#### **4.1 Activity Card Component**
```typescript
interface ActivityCardProps {
  activity: {
    name: string;
    provider: string;
    location: string;
    ageRange: string;
    schedule: string;
    registrationStatus: 'open' | 'closing_soon' | 'full';
    price?: string;
    website?: string;
  };
}

// Features:
// - Registration buttons with real links
// - "Save for Later" functionality  
// - Calendar export integration
// - Share functionality
// - Provider logos/images
```

#### **4.2 Tool Status Component**
```typescript
// Real-time feedback:
const toolMessages = {
  'retrieve_info_from_memory': '🧠 Checking what I know about your family...',
  'url_context_search': '🔍 Searching for current BC activities...',
  'store_activity_preference': '💾 Saving your preferences...',
  'get_current_time': '🕐 Getting current BC time...'
};

// Visual progress indicators, estimated time remaining
```

#### **4.3 Memory Panel**
```typescript
// Shows:
// - Child profiles (name, age, interests)
// - Activity preferences and history
// - Registration timeline
// - Stored facts about family
// - Clear/edit memory options
```

### **Phase 5: Advanced Features** ⏱️ 2-3 days

#### **5.1 Calendar Integration**
```python
@app.post("/api/activity/export")
async def export_activity_to_calendar(activity: ActivityExportRequest):
    """Generate .ics file for activity registration dates"""
    ics_content = create_calendar_event(
        title=f"{activity.name} - {activity.provider}",
        start_time=activity.registration_date,
        location=activity.location,
        description=activity.description
    )
    return Response(ics_content, media_type="text/calendar")
```

#### **5.2 Search Enhancements**
- Auto-complete for BC locations
- Activity type suggestions
- Age-appropriate filtering
- Seasonal recommendations

#### **5.3 Offline Support**
- Service worker for basic functionality
- Cached activity data
- Offline message queuing

### **Phase 6: Testing & Polish** ⏱️ 1-2 days

#### **6.1 Comprehensive Testing**
```python
# Test scenarios:
# - Multi-turn conversations with memory
# - Tool execution with real BC activity searches  
# - Session persistence across browser refreshes
# - Error handling (network issues, ADK failures)
# - Activity card interactions
```

#### **6.2 Performance Optimization**
- Virtualized message lists
- Debounced input
- Memoized components
- Lazy loading

#### **6.3 Error Handling**
- Connection recovery
- Graceful degradation
- User-friendly error messages

---

## 🛠️ **Technology Stack**

### **Frontend**
- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite 5
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Icons**: Lucide React
- **HTTP Client**: Fetch API with custom abstractions

### **Backend** 
- **Framework**: FastAPI (enhanced adk_server.py)
- **Streaming**: Server-Sent Events (SSE)
- **Session**: Dictionary mapping (enhanced from current)
- **Agent**: Google ADK with existing multi_tool_agent

### **Infrastructure**
- **Development**: Local development servers
- **Session Storage**: In-memory with Redis future option
- **File Storage**: Local filesystem
- **Deployment**: Docker containers (future)

---

## 📁 **File Organization**

### **Keep (Enhanced)**
- ✅ `adk_server.py` - Enhanced with SSE streaming
- ✅ `multi_tool_agent/` - Core agent implementation
- ✅ `test_session_management.py` - Still relevant for testing
- ✅ `test_weather_search.py` - Still relevant
- ✅ `README.md` - Updated for new architecture

### **Archive (Completed)**
- 🗂️ `frontend/` → `frontend_deprecated_vercel/` (Vercel AI SDK)
- 🗂️ `frontend_session_example.tsx` → `deprecated/vercel-ui/`
- 🗂️ `test_api.py` → `deprecated/`
- 🗂️ `debug_server.py` → `deprecated/`

### **New Files to Create**
- 📝 `frontend/` - Complete new React/Vite application
- 📝 `backend/requirements.txt` - Consolidated dependencies  
- 📝 `docker-compose.yml` - Future deployment setup
- 📝 `DEPLOYMENT.md` - Deployment instructions

---

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ **Session Persistence**: Conversations maintained across browser refreshes
- ✅ **Tool Transparency**: Users see "Searching for activities..." status
- ✅ **Memory Visualization**: Clear display of stored family information
- ✅ **Rich Activities**: Activity cards with registration links, pricing, schedules
- ✅ **Real-time Updates**: SSE streaming with smooth UX

### **Performance Requirements**
- ⚡ **Initial Load**: < 2 seconds to first interactive state
- ⚡ **Message Response**: < 500ms to show agent typing indicator
- ⚡ **Tool Execution**: Real-time status updates during long operations
- ⚡ **Memory Access**: < 1 second to retrieve and display stored information

### **User Experience Requirements**
- 🎨 **Intuitive Navigation**: Clear chat/memory/activities sections
- 🎨 **Mobile Responsive**: Works well on mobile devices
- 🎨 **Error Recovery**: Graceful handling of connection issues
- 🎨 **Accessibility**: Proper ARIA labels, keyboard navigation

---

## 🚀 **Next Steps**

1. **Phase 1 Start**: Enhance `adk_server.py` with SSE streaming endpoint
2. **Frontend Bootstrap**: Create new React/Vite project structure
3. **Core SSE Connection**: Implement basic SSE communication
4. **Iterative Development**: Build and test components incrementally

**Ready to start coding?** The plan is comprehensive but allows for iterative development and testing at each phase. 