from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext

from ..tools.news_tools import get_news_headlines

news_research_agent = Agent(
    name="news_researcher",
    model="gemini-2.5-flash",
    description="Provides recent news headlines on a requested topic.",
    instruction=(
        "You are a news assistant. Your ONLY task is to call the `get_news_headlines` "
        "tool with the topic supplied by the user. "
        "Respond with a concise, bulleted list of the returned headlines. "
        "If there are no headlines, politely inform the user."
    ),
    tools=[get_news_headlines],
) 