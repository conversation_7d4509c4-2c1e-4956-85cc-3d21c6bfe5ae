#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_burnaby_fix():
    """Test if the Burnaby location filtering fix is working"""
    
    print("🧪 TESTING BURNABY LOCATION FIX")
    print("=" * 40)
    
    # Import the agent
    from multi_tool_agent.agent import agent
    
    # Test query
    query = "swimming classes for 5 year olds in Burnaby"
    print(f"Query: '{query}'")
    print()
    
    try:
        # Run the agent
        print("Running agent...")
        response = await agent.run_async(query)
        
        print("✅ Agent Response:")
        print("-" * 20)
        print(response)
        print()
        
        # Check if response contains Burnaby locations
        response_text = str(response).lower()
        
        has_burnaby = any(term in response_text for term in [
            'burnaby', 'edmonds', 'bonsor', 'eileen dailly', 'shadbolt'
        ])
        
        has_new_west = any(term in response_text for term in [
            'təməsew̓txʷ', 'new westminster'
        ])
        
        print("🔍 ANALYSIS:")
        print(f"  Contains Burnaby locations: {'✅ YES' if has_burnaby else '❌ NO'}")
        print(f"  Contains New West locations: {'❌ YES' if has_new_west else '✅ NO'}")
        
        if has_burnaby and not has_new_west:
            print("\n🎉 SUCCESS: Location filtering is working correctly!")
        elif has_burnaby and has_new_west:
            print("\n⚠️  PARTIAL: Found Burnaby but also New Westminster results")
        else:
            print("\n❌ FAILED: Still returning wrong location results")
            
    except Exception as e:
        print(f"❌ Error running agent: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_burnaby_fix())
