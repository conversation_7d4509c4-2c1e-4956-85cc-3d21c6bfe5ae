import datetime
import asyncio
import logging
import os
import time
import json
from zoneinfo import ZoneInfo
from typing import Optional, Dict, Any, List, Union
from collections import defaultdict
from pathlib import Path

# --- Performance Monitoring ---
_performance_metrics = {
    'search_requests': 0,
    'search_successes': 0,
    'search_failures': 0,
    'cache_hits': 0,
    'cache_misses': 0,
    'avg_response_time': 0.0,
    'total_response_time': 0.0
}

def record_metric(metric_name: str, value: float = 1.0):
    """Record a performance metric."""
    if metric_name in _performance_metrics:
        if metric_name == 'avg_response_time':
            # Calculate running average
            total_requests = _performance_metrics['search_requests']
            if total_requests > 0:
                _performance_metrics['total_response_time'] += value
                _performance_metrics['avg_response_time'] = _performance_metrics['total_response_time'] / total_requests
        else:
            _performance_metrics[metric_name] += value

def get_performance_metrics() -> dict:
    """Get current performance metrics."""
    return _performance_metrics.copy()

from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.tools import google_search
from google.adk.tools import agent_tool
from google.adk.tools.tool_context import ToolContext
from google.adk.planners import BuiltInPlanner
from google import genai as types

# --- Graphiti Core Imports ---
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig as GeminiLLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.search.search_config_recipes import EDGE_HYBRID_SEARCH_RRF, NODE_HYBRID_SEARCH_RRF
from graphiti_core.prompts import Message as GraphitiMessage

# Qdrant Client Imports
from qdrant_client import models

# Configure logging FIRST
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Unified Activity Search import
try:
    # Try relative import first (when running from multi_tool_agent directory)
    from .improved_activity_search import improved_activity_search as get_activity_search
    UNIFIED_SEARCH_AVAILABLE = True
    logger.info("Unified activity search imported successfully (relative)")
except ImportError:
    try:
        # Try direct import (when running from parent directory)
        from multi_tool_agent.unified_activity_search import get_activity_search
        UNIFIED_SEARCH_AVAILABLE = True
        logger.info("Unified activity search imported successfully (absolute)")
    except ImportError:
        try:
            # Try local import (fallback)
            from unified_activity_search import get_activity_search
            UNIFIED_SEARCH_AVAILABLE = True
            logger.info("Unified activity search imported successfully (local)")
        except ImportError as e:
            logger.warning(f"Unified activity search not available: {e}")
            UNIFIED_SEARCH_AVAILABLE = False
            # Fallback: create a dummy function to prevent NameError
            async def get_activity_search(query: str, tool_context) -> dict:
                return {"status": "error", "message": "Unified search tool not available"}

from .filter_extraction_tool import extract_activity_filters

# For memory extraction with generative AI
import google.generativeai as genai
from google.generativeai import GenerativeModel
from google.genai import types
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Pydantic for data models
from pydantic import BaseModel as PydanticBaseModel, Field as PydanticField

# Google OAuth integration
try:
    from .enhanced_auth import authenticate_user_session, setup_enhanced_auth, GOOGLE_LIBS_AVAILABLE
    logger = logging.getLogger(__name__)  # Set up logger early
    logger.info(f"Enhanced auth imported successfully - Google libs available: {GOOGLE_LIBS_AVAILABLE}")
    
    # --- Define the REAL function here ---
    async def authenticate_with_google(args: "GoogleAuthArgs", tool_context: ToolContext) -> dict:
        """
        Authenticate user with Google OAuth and create/retrieve enhanced session.
        
        Args:
            args: The arguments containing the Google ID token.
            tool_context: Current tool context
        """
        if not GOOGLE_LIBS_AVAILABLE:
            return {
                "status": "error",
                "message": "Google authentication is not available. Please check server configuration."
            }
        
        try:
            current_session_id = tool_context._invocation_context.session.id if tool_context._invocation_context.session else None
            google_token = args.google_token
            enhanced_session = await authenticate_user_session(google_token, current_session_id)
            user_profile = enhanced_session.state.data.get("user_profile", {})
            
            return {
                "status": "success",
                "message": f"Successfully authenticated {user_profile.get('name', 'user')}!",
                "user_info": {
                    "name": user_profile.get('name'),
                    "email": user_profile.get('email'),
                    "given_name": user_profile.get('given_name'),
                    "session_id": enhanced_session.id,
                    "user_id": enhanced_session.user_id
                },
                "session_enhanced": True
            }
        except Exception as e:
            logger.error(f"Google authentication failed: {e}")
            return {
                "status": "error",
                "message": f"Authentication failed: {str(e)}",
                "suggestion": "Please ensure you have a valid Google token and try again."
            }

except ImportError as e:
    logger.warning("Google OAuth authentication not available - enhanced_auth module not found")
    
    # --- Define the DUMMY function here ---
    def authenticate_with_google(*args, **kwargs):
        """Dummy function for when Google OAuth dependencies are not installed."""
        return "Google OAuth authentication is not available. Please install required dependencies: pip install PyJWT httpx google-auth"
    
    GOOGLE_LIBS_AVAILABLE = False

try:
    # Try relative import first (for module usage)
    from .config import AgentConfig, TIMEZONE_MAPPING
except ImportError:
    # Fall back to absolute import (for direct execution)
    from config import AgentConfig, TIMEZONE_MAPPING

# --- Simple Configuration Class ---
# Removed: Now imported from .config

# --- Define Entity Types ---
class Person(PydanticBaseModel):
    """Represents a human individual."""
    age: Optional[int] = PydanticField(None, description="The age of the person.")

class Location(PydanticBaseModel):
    """Represents a geographical place."""
    city: Optional[str] = PydanticField(None, description="The city name.")
    country: Optional[str] = PydanticField(None, description="The country name.")

class Interest(PydanticBaseModel):
    """Represents a topic or activity the user is interested in."""
    details: Optional[str] = PydanticField(None, description="Specific details about the interest.")

class Knowledge(PydanticBaseModel):
    """Agent-provided factual information, summaries, or research results."""
    topic: Optional[str] = PydanticField(None, description="The main subject/topic of this knowledge.")
    source: Optional[str] = PydanticField(None, description="Where information came from (search, analysis, etc.)")
    content_type: Optional[str] = PydanticField(None, description="Type of content (summary, research, explanation, etc.)")

class ResearchSession(PydanticBaseModel):
    """A research query and its comprehensive results."""
    query_intent: Optional[str] = PydanticField(None, description="What user was looking for.")
    timestamp: Optional[str] = PydanticField(None, description="When research was conducted.")

# --- Add GoogleAuthArgs model here ---
class GoogleAuthArgs(PydanticBaseModel):
    """Arguments for the Google authentication tool."""
    google_token: str = PydanticField(
        ...,
        description="The Google ID token obtained from the client-side authentication flow."
    )

# --- Enhanced Entity Types for Parent/Child Activity System ---
class Child(PydanticBaseModel):
    """Represents a child whose parent is searching for activities."""
    name: Optional[str] = PydanticField(None, description="Child's name")
    age: Optional[int] = PydanticField(None, description="Current age of the child")
    birth_year: Optional[int] = PydanticField(None, description="Birth year for age-appropriate activity suggestions")
    grade: Optional[str] = PydanticField(None, description="School grade level")
    special_needs: Optional[str] = PydanticField(None, description="Any special considerations or accommodations needed")

class Activity(PydanticBaseModel):
    """Represents a specific activity, program, or event available in BC."""
    name: str = PydanticField(..., description="Name of the activity or program")
    provider: Optional[str] = PydanticField(None, description="Organization providing the activity (e.g., 'Pedalheads', 'City of Vancouver')")
    location_area: Optional[str] = PydanticField(None, description="General area or city where activity is offered")
    age_range: Optional[str] = PydanticField(None, description="Appropriate age range (e.g., '6-12', 'teens')")
    activity_type: Optional[str] = PydanticField(None, description="Category (e.g., 'swimming', 'biking', 'soccer', 'summer_camp', 'art')")
    season: Optional[str] = PydanticField(None, description="When offered (e.g., 'summer', 'year_round', 'winter')")
    registration_period: Optional[str] = PydanticField(None, description="When registration typically opens")

class Registration(PydanticBaseModel):
    """Represents a past or planned registration for an activity."""
    year: int = PydanticField(..., description="Year of registration")
    session: Optional[str] = PydanticField(None, description="Specific session or time period")
    status: str = PydanticField(..., description="Status like 'completed', 'registered', 'interested', 'waitlisted'")
    satisfaction: Optional[str] = PydanticField(None, description="Parent/child feedback ('loved_it', 'okay', 'not_suitable')")

class Preference(PydanticBaseModel):
    """Represents a parent or child preference for activities."""
    preference_type: str = PydanticField(..., description="Type like 'activity_type', 'location', 'time_preference', 'budget'")
    value: str = PydanticField(..., description="The specific preference value")
    strength: Optional[str] = PydanticField("medium", description="How strong the preference is ('strong', 'medium', 'weak')")
    source: Optional[str] = PydanticField("stated", description="How preference was identified ('stated', 'inferred', 'registration_pattern')")

# --- Define Edge Types ---
class HasChild(PydanticBaseModel):
    """Parent-child relationship."""
    relationship: Optional[str] = PydanticField("parent", description="Type like 'parent', 'guardian'")

class LivesIn(PydanticBaseModel):
    """Indicates that a person resides in a location."""
    residence_type: Optional[str] = PydanticField(None, description="Type of residence (e.g., 'permanent', 'temporary', 'visiting')")

class InterestedIn(PydanticBaseModel):
    """Indicates a person's interest in a topic/activity."""
    level_of_interest: Optional[str] = PydanticField(None, description="e.g., 'high', 'casual', 'exploring'")

class ResearchedFor(PydanticBaseModel):
    """Links knowledge/research to the user who requested it."""
    context: Optional[str] = PydanticField(None, description="Why this was researched or requested.")
    relevance_score: Optional[str] = PydanticField(None, description="How relevant this is to the user (high, medium, low)")

class RelatedToInterest(PydanticBaseModel):
    """Links knowledge to user's existing interests."""
    relevance: Optional[str] = PydanticField(None, description="How it relates to their interest.")
    connection_type: Optional[str] = PydanticField(None, description="Type of connection (directly_relevant, tangentially_related, etc.)")

class BasedOnResearch(PydanticBaseModel):
    """Links knowledge to the research session that produced it."""
    extraction_method: Optional[str] = PydanticField(None, description="How knowledge was extracted from research.")
    confidence: Optional[str] = PydanticField(None, description="Confidence level in the extracted knowledge.")

class InterestedInActivity(PydanticBaseModel):
    """Child or parent interest in specific activity type."""
    interest_level: Optional[str] = PydanticField("interested", description="Level like 'very_interested', 'interested', 'considering'")
    discovered_date: Optional[str] = PydanticField(None, description="When this interest was discovered")

class RegisteredFor(PydanticBaseModel):
    """Links child to registration record."""
    registration_date: Optional[str] = PydanticField(None, description="When registration occurred")

class OfferedAt(PydanticBaseModel):
    """Links activity to location where it's offered."""
    frequency: Optional[str] = PydanticField(None, description="How often offered at this location")

class HasPreference(PydanticBaseModel):
    """Links person to their stated preferences."""
    stated_date: Optional[str] = PydanticField(None, description="When preference was expressed")

GRAPHITI_ENTITY_TYPES = {
    "Person": Person,
    "Child": Child,
    "Location": Location,
    "Interest": Interest,
    "Activity": Activity,
    "Registration": Registration,
    "Preference": Preference,
    "Knowledge": Knowledge,
    "ResearchSession": ResearchSession
}

GRAPHITI_EDGE_TYPES = {
    "HAS_CHILD": HasChild,
    "LIVES_IN": LivesIn,
    "INTERESTED_IN": InterestedIn,
    "INTERESTED_IN_ACTIVITY": InterestedInActivity,
    "REGISTERED_FOR": RegisteredFor,
    "OFFERED_AT": OfferedAt,
    "HAS_PREFERENCE": HasPreference,
    "RESEARCHED_FOR": ResearchedFor,
    "RELATED_TO_INTEREST": RelatedToInterest,
    "BASED_ON_RESEARCH": BasedOnResearch
}

GRAPHITI_EDGE_TYPE_MAP = {
    ("Person", "Person"): ["HAS_CHILD"],
    ("Person", "Child"): ["HAS_CHILD"],
    ("Person", "Location"): ["LIVES_IN"],
    ("Person", "Interest"): ["INTERESTED_IN"],
    ("Person", "Preference"): ["HAS_PREFERENCE"],
    ("Child", "Activity"): ["INTERESTED_IN_ACTIVITY"],
    ("Child", "Registration"): ["REGISTERED_FOR"],
    ("Activity", "Location"): ["OFFERED_AT"],
    ("Registration", "Activity"): ["REGISTERED_FOR"],
    ("Person", "Knowledge"): ["RESEARCHED_FOR"],
    ("Person", "ResearchSession"): ["RESEARCHED_FOR"],
    ("Knowledge", "Interest"): ["RELATED_TO_INTEREST"],
    ("Knowledge", "ResearchSession"): ["BASED_ON_RESEARCH"]
}

# The recipes themselves are instances of SearchConfig,
# which internally might use EdgeSearchConfig, NodeSearchConfig etc.

# --- Load Environment Variables ---
load_dotenv()

# --- Validate Configuration ---
AgentConfig.validate()

# --- Initialize Graphiti Client with Better Error Handling ---
try:
    graphiti_client = Graphiti(
        AgentConfig.NEO4J_URI,
        AgentConfig.NEO4J_USER,
        AgentConfig.NEO4J_PASSWORD,
        llm_client=GeminiClient(config=GeminiLLMConfig(api_key=AgentConfig.GOOGLE_API_KEY, model=AgentConfig.GRAPHITI_MODEL)),
        embedder=GeminiEmbedder(config=GeminiEmbedderConfig(api_key=AgentConfig.GOOGLE_API_KEY, embedding_model=AgentConfig.EMBEDDING_MODEL)),
        cross_encoder=None # Explicitly disable the default OpenAI cross-encoder
    )
    # Configure the google.generativeai library with the API key
    genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
    logger.info("Graphiti client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Graphiti client: {e}")
    logger.warning("Agent will continue with limited memory functionality")
    graphiti_client = None

# --- Initialize Qdrant Client with Better Error Handling ---
qdrant_client = None
if AgentConfig.QDRANT_URL:
    try:
        from qdrant_client import AsyncQdrantClient
        qdrant_client = AsyncQdrantClient(
            url=AgentConfig.QDRANT_URL,
            api_key=AgentConfig.QDRANT_API_KEY,
            timeout=30.0  # 30 second timeout
        )
        logger.info(f"Qdrant async client initialized for collection '{AgentConfig.QDRANT_COLLECTION_NAME}'.")
    except Exception as e:
        logger.error(f"Failed to initialize Qdrant async client: {e}. Activity search will be unavailable.")
        qdrant_client = None

# --- Background Save Queue and Workers ---
save_queue = asyncio.Queue(maxsize=AgentConfig.SAVE_QUEUE_MAX_SIZE)
_save_workers_started = False
_save_worker_tasks = [] # To keep references to worker tasks, preventing premature garbage collection
_shutdown_event = asyncio.Event() # Event to signal workers to shut down

async def save_worker():
    """Background worker to save memories to Graphiti."""
    while not _shutdown_event.is_set():
        try:
            # Wait for an item with a timeout to allow periodic shutdown checks
            memory_data = await asyncio.wait_for(save_queue.get(), timeout=1.0)
            
            # Retry logic for saving
            for attempt in range(AgentConfig.MAX_SAVE_RETRIES):
                try:
                    await graphiti_client.add_episode(
                        name=memory_data['name'],
                        episode_body=memory_data['episode_body'],
                        reference_time=memory_data['reference_time']
                    )
                    logger.info(f"Successfully saved memory: {memory_data['name']}")
                    break
                except Exception as e:
                    if attempt < AgentConfig.MAX_SAVE_RETRIES - 1:
                        logger.warning(f"Save attempt {attempt + 1} failed for {memory_data['name']}: {e}. Retrying in {AgentConfig.SAVE_RETRY_DELAY_SECONDS} seconds...")
                        await asyncio.sleep(AgentConfig.SAVE_RETRY_DELAY_SECONDS)
                    else:
                        logger.error(f"Failed to save memory after {AgentConfig.MAX_SAVE_RETRIES} attempts: {memory_data['name']}: {e}")
            
            save_queue.task_done()
        except asyncio.TimeoutError:
            # No item in queue, continue to check shutdown event
            continue
        except Exception as e:
            logger.error(f"Unexpected error in save worker: {e}")
            # Continue running even if there's an unexpected error

def get_current_datetime_utc() -> dict:
    """
    Returns the current date and time in UTC.
    This can be used as a general reference for the current moment.
    """
    print(f"--- Tool: get_current_datetime_utc called ---")
    now_utc = datetime.datetime.now(datetime.timezone.utc)
    report = (
        f'The current UTC date and time is {now_utc.strftime("%Y-%m-%d %H:%M:%S %Z")}'
    )
    return {"status": "success", "report": report, "datetime_utc": now_utc.isoformat()}

# NOTE: get_weather function removed - agent now uses url_context_search for real-time weather queries

async def get_enhanced_activity_search_DEPRECATED_REMOVED(query: str, tool_context: ToolContext) -> dict:
    """
    Enhanced activity search with FIXED age matching, diversity, and back-to-back logic.
    
    This version fixes the critical issues:
    - Proper age range matching (handles database format '(4 - 6)')
    - Ensures activity diversity (not just swimming) 
    - Smart back-to-back scheduling with reasonable gaps
    - Never gives up - always provides helpful results
    """
    
    try:
        # Try the SIMPLE, RELIABLE search function first
        from .simple_age_search import get_simple_age_search
        
        logger.info(f"Using SIMPLE RELIABLE activity search for: {query}")
        result = await get_simple_age_search(query, tool_context)
        
        if result.get("status") == "success" and result.get("count", 0) > 0:
            logger.info(f"Simple search found {result.get('count')} results")
            return result
        elif result.get("status") == "no_age_appropriate":
            logger.info("Simple search found no age-appropriate results - this is correct behavior")
            return result
            
        # If fixed search didn't find enough, try enhanced RAG as backup
        if ENHANCED_RAG_AVAILABLE:
            logger.info("Fixed search incomplete, trying enhanced RAG backup")
            backup_result = await enhanced_activity_search(query, tool_context)
            if backup_result.get("status") == "success":
                return backup_result
                
        # CRITICAL FIX: Don't fall back to unfiltered basic search!
        # If enhanced search failed, provide helpful guidance instead of inappropriate results
        logger.warning("Enhanced search methods failed - providing guidance instead of unfiltered results")
        
        # Extract age from query to provide age-specific guidance
        age_match = None
        import re
        age_patterns = [r'(\d+)\s*year\s*old', r'age\s*(\d+)', r'(\d+)\s*yr']
        for pattern in age_patterns:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                age_match = int(match.group(1))
                break
        
        if age_match:
            age_specific_message = f"For {age_match}-year-olds, I recommend searching for specific activity types like:"
            if age_match <= 5:
                suggestions = ["preschool swimming classes", "toddler art programs", "parent-child activities", "early childhood music classes"]
            elif age_match <= 12:
                suggestions = ["kids swimming lessons", "children's sports programs", "art classes for kids", "music lessons"]
            else:
                suggestions = ["teen fitness programs", "youth sports leagues", "teen art workshops", "high school activities"]
        else:
            age_specific_message = "I recommend searching for specific activity types like:"
            suggestions = ["swimming classes", "art programs", "sports activities", "music lessons"]
        
        return {
            "status": "needs_specific_search",
            "message": f"I need to search more specifically to find age-appropriate activities. {age_specific_message}",
            "suggestions": suggestions,
            "guidance": "Try asking for a specific activity type (like 'swimming for 5 year olds') or let me know the child's age and interests."
        }
            
    except Exception as e:
        logger.error(f"All search methods failed: {e}")

        # Try to provide helpful fallback based on query analysis
        fallback_suggestions = []
        query_lower = query.lower()

        if 'swimming' in query_lower:
            fallback_suggestions.extend([
                "Try 'swimming lessons for kids' or 'aquatic programs'",
                "Check Burnaby recreation centers: Bonsor, Edmonds, Eileen Dailly",
                "Look for 'preschool swimming' or 'learn to swim' programs"
            ])
        elif any(art_term in query_lower for art_term in ['art', 'creative', 'craft', 'painting']):
            fallback_suggestions.extend([
                "Try 'art classes for children' or 'creative programs'",
                "Check Shadbolt Centre for the Arts in Burnaby",
                "Look for 'kids art workshops' or 'creative camps'"
            ])
        elif any(sport_term in query_lower for sport_term in ['soccer', 'basketball', 'hockey', 'sport']):
            fallback_suggestions.extend([
                "Try specific sport names like 'soccer for kids'",
                "Check community centers for sports programs",
                "Look for 'youth sports leagues' or 'recreational sports'"
            ])

        # Add general suggestions
        fallback_suggestions.extend([
            "Try searching for individual activity types (swimming, art, sports)",
            "Look for activities by specific day (Tuesday classes, weekend programs)",
            "Search by location (Burnaby classes, New Westminster programs)",
            "Consider broader age ranges if looking for specific ages"
        ])

        return {
            "status": "search_challenge",
            "message": f"I'm having trouble finding specific matches for '{query}'. Let me suggest some alternatives.",
            "suggestions": fallback_suggestions[:6],  # Limit to 6 suggestions
            "never_give_up": "I'm here to help find activities - let's try a different approach!"
        }

async def get_activity_recommendations_DEPRECATED_REMOVED(query: str, tool_context: ToolContext) -> dict:
    """
    Primary tool for finding community activities. First, it retrieves user's personal context (child's age, location, preferences)
    from memory. Then, it uses this information to search the activity database for relevant programs, classes, and camps.
    This is the main tool to use for any user query related to finding activities.
    """
    start_time = time.time()
    user_id = tool_context._invocation_context.user_id
    
    # 1. --- Get Personal Context from Graphiti/Neo4j ---
    personal_context_info = ""
    if graphiti_client:
        try:
            # Re-use the logic from retrieve_info_from_memory function
            logger.info(f"RAG Tool Step 1: Retrieving personal context for user {user_id}")
            context_results = await retrieve_info_from_memory(query, tool_context)
            if context_results.get("count", 0) > 0:
                personal_context_info = context_results.get("retrieved_information", "")
                logger.info(f"RAG Tool: Found personal context: {personal_context_info[:100]}...")
        except Exception as e:
            logger.warning(f"Could not retrieve personal context from Graphiti: {e}")
            personal_context_info = "Could not retrieve user preferences."
    else:
        logger.warning("Graphiti client not available, skipping personal context retrieval.")

    # 2. --- Search Qdrant Activity Database ---
    if not qdrant_client:
        return {"status": "error", "message": "The activity database connection is currently unavailable."}

    # Enhance the search query with personal context and diversity emphasis
    enhanced_query = f"User query: '{query}'. Search for diverse activity types including swimming, fitness, sports, arts, music, dance, and other programs.\n\nKnown user context: {personal_context_info}"
    logger.info(f"RAG Tool Step 2: Performing Qdrant search with enhanced query: '{enhanced_query}'")
    
    try:
        # Generate embedding for the enhanced query using the same model as CocoIndex
        import google.generativeai as genai
        genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
        
        embedding_response = genai.embed_content(
            model=AgentConfig.EMBEDDING_MODEL,
            content=enhanced_query,
            task_type="RETRIEVAL_QUERY"
        )
        query_vector = embedding_response['embedding']
        
        # Use the vector search with the embedded query
        search_result = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=query_vector,  # Single vector collection - no field name needed
            limit=8,  # Retrieve more results for comprehensive recommendations
            with_payload=True
        )
        
        logger.info(f"Qdrant search completed. Found {len(search_result)} results.")

        if not search_result:
            logger.warning(f"No activities found in Qdrant for query: '{query}'")
            return {
                "status": "not_found", 
                "message": "I couldn't find any specific activities matching that description in our community database."
            }

        # Format results for the LLM
        formatted_activities = []
        for point in search_result:
            payload = point.payload
            
            # Extract information from flat payload structure
            schedule = f"{payload.get('days_of_week', 'N/A')} {payload.get('start_time', 'N/A')}"
            activity_info = (
                f"Activity: {payload.get('name', 'N/A')}\n"
                f"  Category: {payload.get('category', 'N/A')}\n"
                f"  Age Range: {payload.get('age_info', 'N/A')}\n"
                f"  Facility: {payload.get('facility', 'N/A')}\n"
                f"  Schedule: {schedule}\n"
                f"  Dates: {payload.get('start_date', 'N/A')}\n"
                f"  Registration URL: {payload.get('activity_url', 'N/A')}\n"
                f"  Full Details: {payload.get('text', 'No details available')}"
            )
            formatted_activities.append(activity_info)
        
        final_context_for_llm = (
            "--- Retrieved Personal Context ---\n"
            f"{personal_context_info}\n\n"
            "--- Found Matching Community Activities ---\n"
            f"{'\n---\n'.join(formatted_activities)}"
        )
        
        duration = time.time() - start_time
        logger.info(f"RAG Tool completed in {duration:.2f}s. Returning {len(formatted_activities)} activities.")

        return {
            "status": "success",
            "count": len(formatted_activities),
            "retrieved_information": final_context_for_llm
        }
        
    except Exception as e:
        logger.error(f"Error in get_activity_recommendations: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"I encountered an error while searching for activities: {str(e)}. Please try again in a moment.",
            "fallback_suggestions": [
                "Try searching for specific activity types (swimming, art, sports)",
                "Search by location (Burnaby classes, New Westminster programs)",
                "Look for activities by age group",
                "Check community center websites directly"
            ]
        }


async def store_simple_fact(fact: str, tool_context: ToolContext, known_user_name: Optional[str] = None) -> dict:
    """FAST storage for simple facts like names with immediate response."""
    try:
        if len(fact) > 200:
            return {"status": "error", "message": "Fact too long"}
        if not fact or len(fact.strip()) < 3:
            return {"status": "error", "message": "Fact too short"}
        
        user_id = tool_context._invocation_context.user_id
        session_id = tool_context._invocation_context.session.id
        user_name = known_user_name or tool_context._invocation_context.session.state.get('user:user_name', 'User')
        
        cleaned_fact = fact.strip()
        if not cleaned_fact.endswith('.'):
            cleaned_fact += '.'
        
        # Store immediately and wait for completion to ensure facts are available for retrieval
        if graphiti_client:
            try:
                timestamp = int(datetime.datetime.now().timestamp())
                episode_name = f"simple_fact_{user_id}_{timestamp}"
                
                await graphiti_client.add_episode(
                    name=episode_name,
                    episode_body=cleaned_fact,
                    source=EpisodeType.text,
                    source_description=f"Simple fact from {user_name} in session {session_id}",
                    reference_time=datetime.datetime.now(datetime.timezone.utc),
                    group_id=user_id
                )
                
                logger.info(f"Fact stored successfully: '{cleaned_fact[:50]}...' for user {user_id}")
                
                return {
                    "status": "success", 
                    "message": "Got it! I'll remember that.",
                    "fact_stored": cleaned_fact
                }
                
            except Exception as storage_error:
                logger.error(f"Storage failed for '{cleaned_fact[:30]}...': {storage_error}")
                # Try queue as fallback
                try:
                    memory_data = {
                        'name': f"simple_fact_{user_id}_{int(datetime.datetime.now().timestamp())}",
                        'episode_body': cleaned_fact,
                        'reference_time': datetime.datetime.now(datetime.timezone.utc)
                    }
                    await save_queue.put(memory_data)
                    logger.info(f"Fallback: Queued '{cleaned_fact[:30]}...' for background processing")
                    return {
                        "status": "success", 
                        "message": "Got it! I'll remember that.",
                        "fact_stored": cleaned_fact
                    }
                except Exception as queue_error:
                    logger.error(f"Failed to queue fact '{cleaned_fact[:30]}...': {queue_error}")
                    return {"status": "error", "message": f"Storage error: {str(storage_error)}"}
        else:
            return {"status": "error", "message": "Memory system not available"}
            
    except Exception as e:
        logger.error(f"store_simple_fact error: {e}")
        return {"status": "error", "message": f"Storage error: {str(e)}"}

# Removed helper functions - now handled by LLM reasoning

def url_context_search(query: str, url: Optional[str] = None) -> dict:
    """
    Enhanced search tool with URL context using the new Google genai client.
    Can search the web or analyze content from a specific URL.
    
    Args:
        query (str): The search query or question about the URL content
        url (str, optional): A specific URL to analyze for context
        
    Returns:
        dict: Search results with thinking process and URL context if provided
    """
    print(f"🌐 URL_CONTEXT_SEARCH CALLED! Query: '{query}', URL: {url}")
    logger.info(f"URL_CONTEXT_SEARCH TOOL: Query='{query}', URL={url}")
    
    # Basic input validation for security
    if len(query) > 500:
        logger.warning(f"[Security] Search query too long ({len(query)} chars)")
        return {
            "status": "error",
            "message": "Search query is too long. Please use a more concise query."
        }
    
    # URL validation if provided
    if url:
        url_lower = url.lower()
        suspicious_patterns = [
            "javascript:", "data:", "file:", "ftp://localhost", "127.0.0.1", "0.0.0.0"
        ]
        if any(pattern in url_lower for pattern in suspicious_patterns):
            logger.warning(f"[Security] Suspicious URL blocked: {url}")
            return {
                "status": "error",
                "message": "URL appears to be potentially unsafe and cannot be processed."
            }
    
    try:
        # Import here to avoid import issues if the new client isn't available
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=AgentConfig.GOOGLE_API_KEY)
        
        # Configure tools
        tools = []
        if url:
            tools.append(types.Tool(url_context=types.UrlContext()))
        tools.append(types.Tool(google_search=types.GoogleSearch()))
        
        # Create query with URL if provided
        if url:
            content = f"{query} Based on: {url}. Also provide relevant web search context."
        else:
            content = query
            
        # Generate content with thinking budget
        response = client.models.generate_content(
            model=AgentConfig.AGENT_MODEL,
            contents=content,
            config=types.GenerateContentConfig(
                tools=tools,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=384,
                    include_thoughts=True  # Fix: Enable thoughts to be returned
                ),
                response_modalities=["TEXT"],
            )
        )
        
        # Extract thoughts and answer
        thoughts = []
        answer = []
        url_context_data = None
        
        for part in response.candidates[0].content.parts:
            if not part.text:
                continue
            if part.thought:
                thoughts.append(part.text)
            else:
                answer.append(part.text)
        
        # Get URL context metadata if available
        if hasattr(response.candidates[0], 'url_context_metadata'):
            url_context_data = response.candidates[0].url_context_metadata
            
        result = {
            "status": "success",
            "answer": "\n".join(answer) if answer else "No response generated",
            "thinking_summary": "\n".join(thoughts) if thoughts else "No thinking data available",
            "url_context": url_context_data
        }
        
        return result
        
    except ImportError:
        # Fallback to basic search if new client not available
        return {
            "status": "error", 
            "message": "New Google genai client not available. Please install latest version."
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error in URL context search: {str(e)}"
        }

# --- Input Validation and Sanitization ---

def validate_and_sanitize_query(query: str) -> str:
    """Validate and sanitize user input queries."""
    if not query or not isinstance(query, str):
        return ""

    # Remove excessive whitespace and limit length
    query = query.strip()[:500]  # Limit to 500 characters

    # Remove potentially harmful characters but keep basic punctuation
    import re
    query = re.sub(r'[<>{}[\]\\]', '', query)

    return query

# --- Removed Memory Optimization Functions (now handled by LLM reasoning) ---

async def retrieve_info_from_memory(query: str, tool_context: ToolContext) -> dict:
    """
    Retrieves relevant facts and past interactions from the user's memory graph.
    This information provides personal context to better understand and answer the user's current query.
    This tool should be used before searching for activities to gather user preferences.
    """
    start_time = time.time()
    user_id = tool_context._invocation_context.user_id
    
    if not graphiti_client:
        return {"status": "error", "message": "Memory system is unavailable."}

    try:
        logger.info(f"Retrieving memory for user {user_id} with query: '{query}'")
        search_groups = [user_id, "bc_local_user"]
        
        # Parallel search for efficiency
        edge_task = graphiti_client._search(query=query, config=EDGE_HYBRID_SEARCH_RRF, group_ids=search_groups)
        node_task = graphiti_client._search(query=query, config=NODE_HYBRID_SEARCH_RRF, group_ids=search_groups)
        
        results = await asyncio.gather(edge_task, node_task, return_exceptions=True)
        
        all_facts = []
        # Process edge results
        if not isinstance(results[0], Exception) and hasattr(results[0], 'edges'):
            all_facts.extend([edge.fact.strip() for edge in results[0].edges if edge.fact])
        # Process node results
        if not isinstance(results[1], Exception) and hasattr(results[1], 'nodes'):
            for node in results[1].nodes:
                node_text = node.name.strip()
                if hasattr(node, 'summary') and node.summary:
                    node_text = f"{node_text}: {node.summary}"
                all_facts.append(node_text)
        
        unique_facts = sorted(list(set(all_facts)), key=len, reverse=True)
        final_facts = unique_facts[:15]  # Limit context size

        duration = time.time() - start_time
        logger.info(f"Memory retrieval complete: {len(final_facts)} facts in {duration:.2f}s.")

        return {
            "status": "success",
            "retrieved_information": "\n".join(final_facts) if final_facts else "No specific personal information found.",
            "count": len(final_facts)
        }
    except Exception as e:
        logger.error(f"Error retrieving info from memory for user {user_id}: {e}")
        return {"status": "error", "message": f"Could not access memory: {str(e)}"}

async def store_activity_preference(
    child_name: str, 
    activity_type: str, 
    preference_level: str, 
    tool_context: ToolContext
) -> dict:
    """
    Fast storage of child activity preferences for proactive suggestions.
    
    Args:
        child_name: Name of the child
        activity_type: Type of activity (swimming, biking, soccer, etc.)
        preference_level: How much they like it (loves, likes, interested, not_interested)
        tool_context: System context
    """
    try:
        user_id = tool_context._invocation_context.user_id
        session_id = tool_context._invocation_context.session.id
        
        preference_fact = (
            f"{child_name} {preference_level} {activity_type} activities. "
            f"Parent preference noted for future activity suggestions."
        )
        
        timestamp = datetime.datetime.now(datetime.timezone.utc)
        episode_name = f"activity_preference_{child_name}_{activity_type}_{user_id}_{int(timestamp.timestamp())}"
        
        episode_body = (
            f"Child Activity Preference:\n"
            f"EntityType: Preference\n"
            f"preference_type: activity_type\n"
            f"value: {activity_type}\n"
            f"strength: {preference_level}\n"
            f"source: parent_stated\n"
            f"Child: {child_name}\n"
            f"Context: {preference_fact}"
        )

        if graphiti_client:
            memory_data = {
                'name': episode_name,
                'episode_body': episode_body,
                'source_description': f"Activity preference for {child_name}: {preference_level} {activity_type}",
                'source_type': EpisodeType.text,
                'reference_time': timestamp,
                'group_id': user_id
            }
            await save_queue.put(memory_data)
            
            logger.info(f"Stored activity preference: {child_name} {preference_level} {activity_type}")
            return {
                "status": "success",
                "message": f"Got it! I'll remember that {child_name} {preference_level} {activity_type}."
            }
        else:
            return {"status": "error", "message": "Memory system not available"}
            
    except Exception as e:
        logger.error(f"Error storing activity preference: {e}")
        return {"status": "error", "message": f"Failed to store preference: {str(e)}"}

async def store_registration_info(
    child_name: str,
    activity_name: str, 
    provider: str,
    status: str,
    year: int,
    tool_context: ToolContext,
    session: Optional[str] = None
) -> dict:
    """
    Store registration information for tracking and future suggestions.
    
    Args:
        child_name: Name of the child
        activity_name: Name of the specific activity/program
        provider: Organization providing the activity
        status: Registration status (registered, completed, interested, waitlisted)
        year: Year of registration
        tool_context: System context
        session: Optional session info (summer, fall, etc.)
    """
    try:
        user_id = tool_context._invocation_context.user_id
        
        registration_fact = (
            f"{child_name} {status} for {activity_name} with {provider} in {year}"
            + (f" ({session} session)" if session else "") + "."
        )
        
        timestamp = datetime.datetime.now(datetime.timezone.utc)
        episode_name = f"registration_{child_name}_{provider}_{year}_{user_id}_{int(timestamp.timestamp())}"
        
        episode_body = (
            f"Registration Record:\n"
            f"EntityType: Registration\n"
            f"year: {year}\n"
            f"status: {status}\n"
            f"session: {session or 'unspecified'}\n"
            f"Child: {child_name}\n"
            f"Activity: {activity_name}\n"
            f"Provider: {provider}\n"
            f"Context: {registration_fact}"
        )

        if graphiti_client:
            memory_data = {
                'name': episode_name,
                'episode_body': episode_body,
                'source_description': f"Registration: {child_name} -> {activity_name} ({provider}) - {status}",
                'source_type': EpisodeType.text,
                'reference_time': timestamp,
                'group_id': user_id
            }
            await save_queue.put(memory_data)
            
            logger.info(f"Stored registration: {child_name} {status} {activity_name} with {provider}")
            return {
                "status": "success", 
                "message": f"Noted! {child_name}'s {status} registration for {activity_name}."
            }
        else:
            return {"status": "error", "message": "Memory system not available"}
            
    except Exception as e:
        logger.error(f"Error storing registration: {e}")
        return {"status": "error", "message": f"Failed to store registration: {str(e)}"}

def get_proactive_suggestions(query: str, tool_context: ToolContext) -> dict:
    """
    Generate proactive activity suggestions based on stored preferences and current time.
    Fast, rule-based suggestions without additional LLM calls.
    """
    try:
        current_month = datetime.datetime.now().month
        current_year = datetime.datetime.now().year
        
        suggestions = []
        
        # Seasonal suggestions based on current time
        if current_month in [3, 4, 5]:  # Spring
            suggestions.append("Spring soccer programs typically start registration in March-April")
            suggestions.append("Swimming lessons often have spring sessions starting soon")
            
        elif current_month in [6, 7, 8]:  # Summer  
            suggestions.append("Summer camps are in session - check for last-minute availability")
            suggestions.append("Pedalheads summer biking programs may have spots available")
            
        elif current_month in [9, 10, 11]:  # Fall
            suggestions.append("Fall sports registration typically opens in August-September")
            suggestions.append("Consider indoor activities as weather gets cooler")
            
        elif current_month in [12, 1, 2]:  # Winter
            suggestions.append("Early bird summer camp registration often starts in January-February")
            suggestions.append("Winter indoor programs like swimming are great options")
        
        # Registration timing reminders
        if current_month == 1:
            suggestions.append("⏰ Reminder: Summer camp early bird registration often starts now!")
        elif current_month == 2:
            suggestions.append("⏰ Summer activity registration typically opens February-March")
        elif current_month == 8:
            suggestions.append("⏰ Fall sports registration is typically happening now")
            
        return {
            "status": "success",
            "suggestions": suggestions,
            "seasonal_context": f"Current season suggestions for {datetime.datetime.now().strftime('%B %Y')}"
        }
        
    except Exception as e:
        logger.error(f"Error generating proactive suggestions: {e}")
        return {"status": "error", "suggestions": [], "message": str(e)}

# --- Enhanced Current Time Function ---

def get_current_time(location: str = "BC") -> dict:
    """
    Enhanced current time function with full BC timezone support.
    Defaults to BC time if no location specified.
    """
    print(f"--- Tool: get_current_time called for location: {location} ---")
    
    # BC location mapping (all use Pacific Time)
    bc_locations = [
        "bc", "british columbia", "vancouver", "victoria", "burnaby", "richmond", 
        "surrey", "langley", "abbotsford", "kelowna", "kamloops", "prince george",
        "nanaimo", "chilliwack", "new westminster", "coquitlam", "port coquitlam",
        "north vancouver", "west vancouver", "delta", "maple ridge", "pitt meadows"
    ]
    
    location_lower = location.lower().strip()
    
    # Check if it's a BC location
    if any(bc_loc in location_lower for bc_loc in bc_locations):
        bc_time_data = get_bc_current_time()
        if bc_time_data["status"] == "success":
            return {
                "status": "success",
                "location": location,
                "report": f"The current time in {location} is {bc_time_data['bc_formatted']}",
                "datetime": bc_time_data["bc_datetime"],
                "timezone": bc_time_data["timezone"],
                "seasonal_context": bc_time_data["seasonal_context"],
                "timing_alerts": get_activity_timing_alerts(bc_time_data["month"], bc_time_data["year"])
            }
    
    # Fallback for other locations
    timezone_map = {
        "new york": "America/New_York",
        "toronto": "America/Toronto",
        "calgary": "America/Edmonton",
        "edmonton": "America/Edmonton"
    }
    
    tz_identifier = timezone_map.get(location_lower)
    if not tz_identifier:
        return {
            "status": "error",
            "error_message": f"Sorry, I don't have timezone information for {location}. I can provide BC time by default."
        }
    
    try:
        tz = ZoneInfo(tz_identifier)
        now = datetime.datetime.now(tz)
        return {
            "status": "success",
            "location": location,
            "report": f"The current time in {location} is {now.strftime('%Y-%m-%d %H:%M:%S %Z%z')}",
            "datetime": now.isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Error getting time for {location}: {str(e)}"
        }

# --- Auto-Time Awareness for Agent Context (MOVED TO BEFORE AGENT DEFINITION) ---

def get_agent_time_context() -> str:
    """
    Get formatted time context that's automatically available to the agent.
    This ensures the agent always knows the current BC time and seasonal context.
    """
    bc_time = get_bc_current_time()
    if bc_time["status"] == "success":
        timing_alerts = get_activity_timing_alerts(bc_time["month"], bc_time["year"])
        seasonal = bc_time["seasonal_context"]
        
        context = f"""
CURRENT TIME CONTEXT (Auto-Updated):
- BC Time: {bc_time['bc_formatted']} ({bc_time['timezone']})
- Season: {seasonal['season'].title()} in British Columbia
- Current Focus: {seasonal['registration_focus']}
- Timing Note: {seasonal['timing_note']}

IMMEDIATE TIMING ALERTS:
{chr(10).join([f"  • {alert}" for alert in timing_alerts[:3]])}

This context is automatically provided - use it for time-sensitive suggestions!
"""
        return context
    else:
        return "TIME CONTEXT: Unable to determine current BC time"

# --- Tool and Agent Definition Section ---

# 1. Start with a base list of tools that are always available.
# For activity searches, we prioritize local database tools
tools = [
    extract_activity_filters,
    get_activity_search,
    retrieve_info_from_memory,
    store_simple_fact,
    store_activity_preference,
    store_registration_info,
    get_current_time,
    # Note: url_context_search deliberately excluded from main tools
    # to prevent agent from using it for activity searches
]

# 2. Try to import optional dependencies and define the optional tool.
try:
    from .enhanced_auth import authenticate_user_session, setup_enhanced_auth, GOOGLE_LIBS_AVAILABLE
    logger.info(f"Enhanced auth imported successfully - Google libs available: {GOOGLE_LIBS_AVAILABLE}")

    class GoogleAuthArgs(PydanticBaseModel):
        """Arguments for the Google authentication tool."""
        google_token: str = PydanticField(
            ...,
            description="The Google ID token obtained from the client-side authentication flow."
        )

    async def authenticate_with_google(args: GoogleAuthArgs, tool_context: ToolContext) -> dict:
        """
        Authenticate user with Google OAuth and create/retrieve enhanced session.
        
        Args:
            args: The arguments containing the Google ID token.
            tool_context: Current tool context
        """
        if not GOOGLE_LIBS_AVAILABLE:
            return {"status": "error", "message": "Google auth dependencies not fully available."}
        
        try:
            current_session_id = tool_context._invocation_context.session.id if tool_context._invocation_context.session else None
            google_token = args.google_token
            enhanced_session = await authenticate_user_session(google_token, current_session_id)
            user_profile = enhanced_session.state.data.get("user_profile", {})
            
            return {
                "status": "success",
                "message": f"Successfully authenticated {user_profile.get('name', 'user')}!",
                "user_info": {
                    "name": user_profile.get('name'), "email": user_profile.get('email'),
                    "given_name": user_profile.get('given_name'), "session_id": enhanced_session.id,
                    "user_id": enhanced_session.user_id
                },
                "session_enhanced": True
            }
        except Exception as e:
            logger.error(f"Google authentication failed: {e}")
            return {"status": "error", "message": f"Authentication failed: {str(e)}"}

    # 3. If successful, append the optional tool to the list.
    tools.append(authenticate_with_google)
    logger.info("`authenticate_with_google` tool has been enabled.")

except ImportError:
    logger.warning("Google OAuth authentication not available (enhanced_auth module not found). The `authenticate_with_google` tool will be disabled.")
    # If the import fails, we simply do nothing. The tool is not added to the list.

# 4. Define the agent with the final, dynamically constructed list of tools.
agent = Agent(
    name="bc_parent_activity_assistant",
    model=AgentConfig.SEARCH_AGENT_MODEL,  # Use a cost-effective model for internal search logic
    description=(
        "A specialized AI assistant that helps parents in British Columbia find and register for children's activities."
    ),
    instruction=(
        "You are a friendly and expert assistant for parents in British Columbia. Your primary function is to help them find children's activities using our local activity database.\n\n"

        "**CRITICAL: ACTIVITY SEARCH PROTOCOL**\n"
        "For ANY query about children's activities, classes, camps, or programs:\n"
        "1. ALWAYS call `extract_activity_filters` first to parse the query\n"
        "2. ALWAYS call `get_activity_search` second with the query and extracted filters\n"
        "3. NEVER use any other search tools for activity queries\n\n"

        "**SUPPORTED LOCATIONS (Local Database):**\n"
        "- Burnaby (full coverage)\n"
        "- New Westminster (full coverage)\n"
        "- Other BC cities (limited coverage)\n\n"

        "**YOUR PROCESS:**\n"
        "1. **Extract Filters:** Call `extract_activity_filters(query)`\n"
        "2. **Search Database:** Call `get_activity_search(query, filters, tool_context)`\n"
        "3. **Present Results:** Format results with venue details and booking links\n"
        "4. **If No Results:** Suggest trying different search terms or nearby cities\n\n"
        "    -   **Venue Specificity:** When presenting results, you MUST include the specific venue or facility name (e.g., 'Bonsor Recreation Complex', 'Rosemary Brown Recreation Centre') from the `facility` or `location` field in the search results. Do not just state the city.\n"
        "    -   **If `search_type` is 'consecutive':** The `results` will be a list of paired activities. You MUST verify that **both** activities in each pair are age-appropriate based on the user's request. Present the confirmed pairs, clearly stating the two activities, their times, and the specific venue. If the list is empty, you must state that you searched but could not find any age-appropriate activities that could be scheduled back-to-back.\n"
        "    -   **If `search_type` is 'standard':** The `results` will be a list of individual activities. Present the most relevant options in a clear, easy-to-read list. For each activity, you MUST include the specific venue. Highlight why they are a good match for the user's request.\n"
        "    -   **Include Booking Links:** For every activity you list, you MUST check the `metadata` for an `activity_url` and include it in your response. Format it as a clickable markdown link, like `Book Now`.\n"
        "    -   **If `search_type` is 'simultaneous':** The `results` will be a list of paired activities. You MUST present these as confirmed simultaneous options, ensuring **each activity in the pair** covers the requested ages. Clearly state the two activities, their times, and the specific venue. If the list is empty, you must state that you searched but could not find any age-appropriate activities that could be scheduled at the same time.\n"
        "    -   **Never give up:** If the search returns no results, do not just stop. Suggest alternative search criteria, like a different category, location, or a broader age range.\n\n"

        "**EXAMPLES - ALWAYS FOLLOW THIS PATTERN:**\n\n"

        "**Burnaby Classes Example:**\n"
        "User: 'Tell me about Burnaby classes for 5-year-olds'\n"
        "Step 1: extract_activity_filters('Tell me about Burnaby classes for 5-year-olds')\n"
        "Step 2: get_activity_search('Tell me about Burnaby classes for 5-year-olds', filters, tool_context)\n"
        "Result: Local database results with Burnaby activities\n\n"

        "**Art Classes Example:**\n"
        "User: 'What art classes are available in Burnaby?'\n"
        "Step 1: extract_activity_filters('What art classes are available in Burnaby?')\n"
        "Step 2: get_activity_search('What art classes are available in Burnaby?', filters, tool_context)\n"
        "Result: Local database results with art classes\n\n"

        "**Swimming Lessons Example:**\n"
        "User: 'Swimming lessons for kids in New Westminster'\n"
        "Step 1: extract_activity_filters('Swimming lessons for kids in New Westminster')\n"
        "Step 2: get_activity_search('Swimming lessons for kids in New Westminster', filters, tool_context)\n"
        "Result: Local database results with swimming programs\n\n"
    ),
    tools=tools,
)

# Required by Google ADK framework
_root_legacy_agent = agent
# Temporarily use the direct agent instead of orchestrator to fix data flow issues
root_agent = agent

def filter_data_quality(information_list):
    """Filter out low-quality or irrelevant information."""
    if not information_list:
        return []
    
    filtered = []
    for info in information_list:
        # Basic quality checks
        if isinstance(info, str) and len(info.strip()) > 5:
            # Remove very short or empty strings
            if not any(skip_word in info.lower() for skip_word in ['test', 'debug', 'placeholder']):
                filtered.append(info.strip())
    
    return filtered

def detect_conversation_patterns(query, session_history=None):
    """Detect patterns in conversation for contextual responses."""
    patterns = {
        'repeat_query': False,
        'follow_up': False,
        'clarification': False
    }
    
    if not query:
        return patterns
    
    query_lower = query.lower()
    
    # Detect repeat queries
    repeat_indicators = ['again', 'repeat', 'what was', 'remind me']
    patterns['repeat_query'] = any(indicator in query_lower for indicator in repeat_indicators)
    
    # Detect follow-up questions
    followup_indicators = ['also', 'and', 'what about', 'how about']
    patterns['follow_up'] = any(indicator in query_lower for indicator in followup_indicators)
    
    # Detect clarification requests
    clarification_indicators = ['what do you mean', 'explain', 'clarify', 'more details']
    patterns['clarification'] = any(indicator in query_lower for indicator in clarification_indicators)
    
    return patterns

# Simple in-memory cache for search results
_search_cache = {}
_cache_max_size = 100
_cache_ttl = 300  # 5 minutes

def enhance_memory_retrieval_with_caching(query, user_id):
    """Enhance memory retrieval with caching mechanisms."""
    import time

    # Simple cache key based on query and user
    cache_key = f"{user_id}:{hash(query)}"
    current_time = time.time()

    # Check if we have a valid cached result
    if cache_key in _search_cache:
        cached_entry = _search_cache[cache_key]
        if current_time - cached_entry['timestamp'] < _cache_ttl:
            logger.info(f"Cache hit for query: {query[:50]}...")
            return {
                'status': 'cache_hit',
                'cache_hit': True,
                'cached_data': cached_entry['data'],
                'cache_key': cache_key
            }
        else:
            # Expired cache entry
            del _search_cache[cache_key]

    # Cache miss - clean up old entries if cache is full
    if len(_search_cache) >= _cache_max_size:
        # Remove oldest entries
        sorted_entries = sorted(_search_cache.items(), key=lambda x: x[1]['timestamp'])
        for key, _ in sorted_entries[:_cache_max_size // 2]:
            del _search_cache[key]

    return {
        'status': 'cache_miss',
        'cache_hit': False,
        'cached_data': None,
        'cache_key': cache_key
    }

def cache_search_result(cache_key, data):
    """Cache a search result."""
    import time
    _search_cache[cache_key] = {
        'data': data,
        'timestamp': time.time()
    }

def optimize_memory_query_for_new_info(query, session_history=None):
    """Optimize query to focus on new information."""
    if not query:
        return query
    
    # Simple optimization - add context keywords
    optimized = query
    
    # Add user context keywords if not present
    user_keywords = ['user', 'my', 'personal']
    if not any(keyword in query.lower() for keyword in user_keywords):
        optimized = f"user {query}"
    
    return optimized

# Add missing helper functions for memory system
def get_unshared_information(tool_context: ToolContext, information_list: list) -> list:
    """Filter out information that has already been shared in this session."""
    if not information_list:
        return []
    
    # Simple implementation - in a real system this would check session state
    # For now, just return all information (no session tracking)
    return information_list

async def track_shared_information(tool_context: ToolContext, information_list: list):
    """Track information that has been shared with the user in this session."""
    # Simple implementation - in a real system this would store in session state
    # For now, just log that information was shared
    logger.info(f"Marked {len(information_list)} pieces of information as shared")

async def update_memory_cache(tool_context: ToolContext, query: str, result: dict):
    """Update memory cache with search results."""
    # Simple implementation - in a real system this would use Redis or similar
    logger.debug(f"Memory cache updated for query: '{query}'")

def get_conversation_context_summary(tool_context: ToolContext) -> dict:
    """Get summary of conversation context."""
    return {
        "session_id": tool_context._invocation_context.session.id,
        "user_id": tool_context._invocation_context.user_id
    }

# --- Enhanced Time and Location Awareness ---

def get_bc_current_time() -> dict:
    """
    Get current time in BC (Pacific Time) without requiring user input.
    This provides foolproof time awareness for the agent.
    """
    try:
        # BC uses Pacific Time (Vancouver timezone)
        bc_tz = ZoneInfo("America/Vancouver")
        bc_time = datetime.datetime.now(bc_tz)
        utc_time = datetime.datetime.now(datetime.timezone.utc)
        
        # Determine if it's PST or PDT
        is_dst = bc_time.dst() != datetime.timedelta(0)
        tz_name = "PDT" if is_dst else "PST"
        
        return {
            "status": "success",
            "bc_datetime": bc_time.isoformat(),
            "bc_formatted": bc_time.strftime("%Y-%m-%d %H:%M:%S %Z"),
            "bc_date": bc_time.strftime("%Y-%m-%d"),
            "bc_time": bc_time.strftime("%H:%M:%S"),
            "timezone": tz_name,
            "is_dst": is_dst,
            "month": bc_time.month,
            "year": bc_time.year,
            "day_of_week": bc_time.strftime("%A"),
            "utc_datetime": utc_time.isoformat(),
            "seasonal_context": get_seasonal_context(bc_time.month)
        }
    except Exception as e:
        logger.error(f"Error getting BC time: {e}")
        return {
            "status": "error",
            "message": f"Could not determine BC time: {str(e)}"
        }

def get_seasonal_context(month: int) -> dict:
    """
    Get seasonal context for BC activity planning.
    """
    seasons = {
        "winter": [12, 1, 2],
        "spring": [3, 4, 5], 
        "summer": [6, 7, 8],
        "fall": [9, 10, 11]
    }
    
    current_season = None
    for season, months in seasons.items():
        if month in months:
            current_season = season
            break
    
    # Registration periods and activity seasons in BC
    seasonal_info = {
        "winter": {
            "activities": ["indoor swimming", "ice skating", "skiing", "indoor sports"],
            "registration_focus": "Summer camp early bird registration (Jan-Feb)",
            "timing_note": "Best time to plan ahead for summer activities"
        },
        "spring": {
            "activities": ["outdoor soccer", "biking", "spring camps", "hiking"],
            "registration_focus": "Summer activity registration peak (Mar-May)",
            "timing_note": "Many summer programs fill up during spring registration"
        },
        "summer": {
            "activities": ["swimming", "outdoor camps", "biking", "water sports"],
            "registration_focus": "Fall activity registration (Jul-Aug)",
            "timing_note": "Summer activities in session, fall planning time"
        },
        "fall": {
            "activities": ["indoor sports", "art classes", "music lessons", "fall soccer"],
            "registration_focus": "Winter and next year planning",
            "timing_note": "School year activities, early planning for next summer"
        }
    }
    
    return {
        "season": current_season,
        "seasonal_activities": seasonal_info.get(current_season, {}).get("activities", []),
        "registration_focus": seasonal_info.get(current_season, {}).get("registration_focus", ""),
        "timing_note": seasonal_info.get(current_season, {}).get("timing_note", "")
    }

def get_activity_timing_alerts(current_month: int, current_year: int) -> list:
    """
    Generate time-sensitive activity alerts based on current BC time.
    """
    alerts = []
    
    # Monthly timing alerts for BC activities
    timing_calendar = {
        1: [
            "🏊 Winter swimming lessons - great time to start indoor activities",
            "📅 Summer camp early bird registration opens at many providers",
            "🎿 Peak winter sports season in BC mountains"
        ],
        2: [
            "📋 Summer camp registration in full swing - early bird discounts available",
            "🏀 Winter indoor sports programs accepting registrations",
            "🎯 Plan ahead: summer activity research time"
        ],
        3: [
            "⚽ Spring soccer registration typically opens",
            "🚴 Pedalheads early registration for summer biking programs",
            "🏊 Spring swimming lesson sessions start"
        ],
        4: [
            "🏕️ Summer camp registration peak month - don't wait!",
            "🌸 Spring outdoor activities resuming",
            "📅 Many popular summer programs fill up this month"
        ],
        5: [
            "⚠️ Last chance for summer camp registration at popular providers",
            "🚴 Spring biking season - great time for skill lessons",
            "🏊 Outdoor pool seasons preparing to open"
        ],
        6: [
            "🏊 Outdoor swimming season begins",
            "🏕️ Summer camps starting - check waitlists for openings",
            "🚴 Peak biking season - Pedalheads programs in full swing"
        ],
        7: [
            "🏖️ Summer activities at peak - mid-season camp availability",
            "📅 Fall activity registration opens at many providers",
            "🏊 Prime time for swimming lessons and water activities"
        ],
        8: [
            "⚽ Fall soccer registration typically opens",
            "🎒 Back-to-school activity planning time",
            "🏊 Last month for many outdoor programs"
        ],
        9: [
            "🏫 School year activities starting",
            "🍂 Fall indoor program registration",
            "⚽ Fall sports seasons begin"
        ],
        10: [
            "🎨 Winter indoor activities (art, music) registration opens",
            "🏊 Indoor swimming lesson sessions resuming",
            "🏀 Winter sports preparation time"
        ],
        11: [
            "❄️ Winter activity planning - indoor sports, skiing prep",
            "📅 Early planning for next year's activities",
            "🏊 Indoor pool programs in full swing"
        ],
        12: [
            "🎿 Winter sports season begins",
            "🎄 Holiday camp programs available",
            "📋 Research time for next year's summer activities"
        ]
    }
    
    current_alerts = timing_calendar.get(current_month, [])
    
    # Add year-specific context
    for alert in current_alerts:
        alerts.append(f"{alert} ({current_year})")
    
    return alerts
