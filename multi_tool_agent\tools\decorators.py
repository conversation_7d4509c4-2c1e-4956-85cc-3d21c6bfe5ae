"""Common decorators for tool functions."""

from functools import wraps
import logging
from typing import Callable, Any, Dict

logger = logging.getLogger(__name__)

def tool_safe(default_error: str = "Unexpected error") -> Callable[[Callable[..., Any]], Callable[..., Dict[str, Any]]]:
    """Decorator to ensure every tool returns a standard dict with status key.

    Usage:
        @tool_safe()
        def my_tool(...):
            ...
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Dict[str, Any]]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Dict[str, Any]:
            try:
                result = func(*args, **kwargs)
                # If tool already returns a dict with status, pass through
                if isinstance(result, dict) and "status" in result:
                    return result
                # Otherwise wrap
                return {"status": "success", "result": result}
            except Exception as e:
                logger.error("Tool %s failed: %s", func.__name__, e, exc_info=True)
                return {"status": "error", "message": str(e) or default_error}

        return wrapper

    return decorator 