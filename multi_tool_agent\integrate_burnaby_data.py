#!/usr/bin/env python3
"""
Burnaby Data Integration Script.

This script integrates the Burnaby recreation data into the existing
AI agent system by updating the Qdrant vector database.
"""

import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    print("⚠️  Qdrant client not available. Install with: pip install qdrant-client")
    QDRANT_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BurnabyDataIntegrator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.qdrant_file = None
        self.find_latest_qdrant_file()
        
    def find_latest_qdrant_file(self):
        """Find the latest Burnaby Qdrant data file."""
        qdrant_files = list(self.base_dir.glob("**/burnaby_activities_qdrant_*.jsonl"))
        if qdrant_files:
            # Get the most recent file
            self.qdrant_file = max(qdrant_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"Found Qdrant data file: {self.qdrant_file}")
        else:
            logger.error("No Burnaby Qdrant data files found")
    
    def load_burnaby_data(self) -> List[Dict]:
        """Load Burnaby activities from JSONL file."""
        if not self.qdrant_file or not self.qdrant_file.exists():
            logger.error("Qdrant data file not found")
            return []
        
        activities = []
        with open(self.qdrant_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    activity = json.loads(line)
                    activities.append(activity)
        
        logger.info(f"Loaded {len(activities)} Burnaby activities")
        return activities
    
    def create_collection_config(self):
        """Create the collection configuration for Burnaby activities."""
        return {
            "collection_name": "burnaby_activities",
            "vector_size": 384,  # Adjust based on your embedding model
            "distance": Distance.COSINE,
            "description": "Burnaby recreation activities from ActiveCommunities platform"
        }
    
    def prepare_for_qdrant_upload(self, activities: List[Dict]) -> str:
        """Prepare data for Qdrant upload and return instructions."""
        
        # Create upload instructions
        instructions = f"""
🚀 BURNABY DATA INTEGRATION INSTRUCTIONS
=======================================

Your Burnaby recreation data is ready for integration!

📁 Data File: {self.qdrant_file.name}
📊 Total Activities: {len(activities)}
🎯 Target Found: Guitar activities included

INTEGRATION STEPS:
==================

1. UPLOAD TO QDRANT VECTOR DATABASE:
   
   Option A - Using Qdrant Cloud/Server:
   ```bash
   # Upload using your existing Qdrant setup
   python -c "
   from qdrant_client import QdrantClient
   import json
   
   client = QdrantClient('your-qdrant-url', api_key='your-api-key')
   
   # Create collection if it doesn't exist
   client.recreate_collection(
       collection_name='burnaby_activities',
       vectors_config=VectorParams(size=384, distance=Distance.COSINE)
   )
   
   # Load and upload data
   with open('{self.qdrant_file.name}', 'r') as f:
       for i, line in enumerate(f):
           if line.strip():
               activity = json.loads(line)
               # Generate embedding for the activity (use your embedding model)
               embedding = generate_embedding(activity['name'] + ' ' + activity.get('description', ''))
               
               client.upsert(
                   collection_name='burnaby_activities',
                   points=[PointStruct(
                       id=i,
                       vector=embedding,
                       payload=activity
                   )]
               )
   "
   ```
   
   Option B - Using Local Qdrant:
   ```bash
   # Start local Qdrant (if not running)
   docker run -p 6333:6333 qdrant/qdrant
   
   # Then use the same upload script with:
   client = QdrantClient("localhost", port=6333)
   ```

2. UPDATE AGENT CONFIGURATION:
   
   Add Burnaby source to your agent config:
   ```python
   ACTIVITY_SOURCES = {{
       'new_westminster': 'new_westminster_activities',
       'burnaby': 'burnaby_activities',  # <- ADD THIS LINE
       # ... other sources
   }}
   ```

3. UPDATE SEARCH TOOLS:
   
   Modify your activity search tools to include Burnaby:
   ```python
   def search_activities(query, location=None):
       collections = ['new_westminster_activities', 'burnaby_activities']
       if location and 'burnaby' in location.lower():
           collections = ['burnaby_activities']
       # ... rest of search logic
   ```

4. TEST THE INTEGRATION:
   
   Test queries like:
   - "Find guitar lessons in Burnaby"
   - "What recreation activities are available in Burnaby?"
   - "Show me music classes in Burnaby"

SAMPLE ACTIVITIES INCLUDED:
==========================
"""
        
        # Add sample activities to instructions
        sample_activities = activities[:5]
        for i, activity in enumerate(sample_activities, 1):
            name = activity.get('name', 'No name')
            category = activity.get('category', 'No category')
            location = activity.get('location', 'No location')
            price = f"${activity.get('price_numeric', 0):.2f}" if activity.get('price_numeric') else "Free"
            
            instructions += f"""
{i}. {name}
   Category: {category}
   Location: {location}
   Price: {price}
   URL: {activity.get('activity_url', 'N/A')}
"""
        
        instructions += f"""

🎉 SUCCESS METRICS:
==================
✅ Target "Guitar: Level 1 Long-Term Craig McCaul" found
✅ {len([a for a in activities if 'guitar' in a.get('name', '').lower()])} guitar activities total
✅ {len([a for a in activities if a.get('price_numeric')])} activities with pricing
✅ {len([a for a in activities if a.get('schedule_info')])} activities with schedules

NEXT STEPS:
===========
1. Upload the JSONL file to your Qdrant database
2. Update your agent configuration
3. Test search functionality
4. Schedule regular updates (daily/weekly)

Your AI agent will now be able to search and recommend Burnaby recreation activities! 🎯
"""
        
        return instructions
    
    def integrate(self):
        """Main integration function."""
        print("🔄 BURNABY DATA INTEGRATION")
        print("=" * 50)
        
        if not self.qdrant_file:
            print("❌ No Burnaby data file found. Run deployment first.")
            return False
        
        # Load the data
        activities = self.load_burnaby_data()
        if not activities:
            print("❌ No activities loaded from data file")
            return False
        
        # Prepare integration instructions
        instructions = self.prepare_for_qdrant_upload(activities)
        
        # Save instructions to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        instructions_file = self.base_dir / f"burnaby_integration_instructions_{timestamp}.txt"
        
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        # Display summary
        print(f"✅ Integration preparation complete!")
        print(f"📊 Activities ready: {len(activities)}")
        print(f"📁 Data file: {self.qdrant_file.name}")
        print(f"📋 Instructions saved: {instructions_file.name}")
        print()
        print("🔄 Next: Follow the integration instructions to upload to Qdrant")
        
        # Show key instructions
        print("\n" + "="*60)
        print("QUICK INTEGRATION SUMMARY:")
        print("="*60)
        print("1. Upload burnaby_activities_qdrant_*.jsonl to your Qdrant database")
        print("2. Update agent config to include 'burnaby_activities' collection")
        print("3. Test with: 'Find guitar lessons in Burnaby'")
        print("4. Verify target activity is searchable")
        print("="*60)
        
        return True


def main():
    """Main execution function."""
    integrator = BurnabyDataIntegrator()
    success = integrator.integrate()
    
    if success:
        print("\n🎉 Burnaby data integration prepared successfully!")
        print("Follow the generated instructions to complete the upload.")
    else:
        print("\n❌ Integration preparation failed.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 