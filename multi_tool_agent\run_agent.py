import asyncio
import logging
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the agent
from multi_tool_agent.agent import root_agent

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.agents.run_config import RunConfig, StreamingMode  # Add streaming config imports
from google.genai import types # For creating message Content/Parts

# Configure basic logging to see ADK's internal INFO messages if needed,
# or set to ERROR for less verbosity.
logging.basicConfig(level=logging.INFO) # Changed to INFO to see streaming logs


async def call_agent_with_streaming(query: str, runner: Runner, user_id: str, session_id: str):
    """Sends a query to the agent and handles streaming events properly."""
    print(f"\n>>> User Query: {query}")

    # Prepare the user's message in ADK format
    content = types.Content(role='user', parts=[types.Part(text=query)])

    final_response_text = "Agent did not produce a final response."  # Default
    streaming_response_parts = []  # Collect streaming parts
    thought_parts = []  # Collect thinking parts separately
    total_events = 0
    
    # Configure streaming mode for SSE (Server-Sent Events)
    run_config = RunConfig(streaming_mode=StreamingMode.SSE)
    
    # run_async executes the agent logic and yields Events.
    async for event in runner.run_async(
        user_id=user_id, 
        session_id=session_id, 
        new_message=content, 
        run_config=run_config  # Enable streaming
    ):
        total_events += 1
        
        # Log all events for debugging streaming
        print(f"  [Event {total_events}] Author: {event.author}, Type: {type(event).__name__}, "
              f"Final: {event.is_final_response()}, Partial: {getattr(event, 'partial', False)}")

        # Handle both streaming (partial) and final events
        if event.content and event.content.parts:
            for part in event.content.parts:
                if hasattr(part, 'text') and part.text:
                    # Check if this is a thought part
                    if getattr(part, 'thought', False):
                        thought_parts.append(part.text)
                        print(f"  [Thinking] {part.text[:100]}...")
                    else:
                        # For streaming, collect parts; for final, use directly
                        if getattr(event, 'partial', False):
                            streaming_response_parts.append(part.text)
                            print(f"  [Streaming] {part.text}")
                        elif event.is_final_response():
                            # Final event - use collected streaming parts or this final text
                            if streaming_response_parts:
                                final_response_text = "".join(streaming_response_parts).strip()
                            else:
                                final_response_text = part.text.strip()

        # Handle final response event
        if event.is_final_response():
            # If we collected streaming parts, use those
            if streaming_response_parts:
                final_response_text = "".join(streaming_response_parts).strip()
            # Handle escalation/error cases
            elif event.actions and event.actions.escalate:
                final_response_text = f"Agent escalated: {event.error_message or 'No specific message.'}"
            break  # Stop processing events once the final response is found

    print(f"<<< Agent Response: {final_response_text}")
    print(f"--- Event Summary: {total_events} total events, "
          f"{len(streaming_response_parts)} streaming parts, "
          f"{len(thought_parts)} thought parts")
    
    return {
        "response": final_response_text,
        "total_events": total_events,
        "streaming_parts": len(streaming_response_parts),
        "thought_parts": len(thought_parts),
        "has_streaming": len(streaming_response_parts) > 0
    }


async def main():
    """Sets up and runs the agent conversation with streaming test."""
    # --- Session Management ---
    session_service = InMemorySessionService()

    APP_NAME = "streaming_test_app"
    USER_ID = "user_streaming_test"
    SESSION_ID = "session_streaming_001"

    await session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
        session_id=SESSION_ID,
        # Initialize session state with user_id and session_id for tools to access
        state={
            "user_id": USER_ID,
            "session_id": SESSION_ID
        }
    )
    print(f"Session created: App='{APP_NAME}', User='{USER_ID}', Session='{SESSION_ID}'")

    # --- Runner ---
    runner = Runner(
        agent=root_agent,  # Your imported agent with streaming-compatible model
        app_name=APP_NAME,
        session_service=session_service
    )
    print(f"Runner created for agent '{runner.agent.name}' using model '{runner.agent.model}'.")

    # --- Test streaming with different queries ---
    test_queries = [
        "Tell me about Burnaby classes for 5-year-olds", # This should use activity search, not web search
        "What art classes are available in Burnaby for kids?", # Another activity search test
        "What time is it in New York?", # Tool usage - should trigger thinking
    ]
    
    streaming_results = []
    
    for query in test_queries:
        result = await call_agent_with_streaming(query, runner, USER_ID, SESSION_ID)
        streaming_results.append({
            "query": query,
            "result": result
        })
        print("-" * 80)
    
    # Summary of streaming test
    print("\n=== STREAMING TEST SUMMARY ===")
    total_streaming_events = sum(r["result"]["streaming_parts"] for r in streaming_results)
    
    for test in streaming_results:
        query = test["query"]
        result = test["result"]
        streaming_status = "✅ STREAMING" if result["has_streaming"] else "❌ NO STREAMING"
        print(f"Query: '{query}'")
        print(f"  {streaming_status} - {result['streaming_parts']} streaming parts, "
              f"{result['thought_parts']} thought parts")
    
    if total_streaming_events > 0:
        print(f"\n🎉 SUCCESS: Streaming is working! Total streaming events: {total_streaming_events}")
    else:
        print(f"\n⚠️  WARNING: No streaming events detected. Check model compatibility.")
        print("Consider using 'adk web' command for better streaming support.")


if __name__ == "__main__":
    asyncio.run(main())