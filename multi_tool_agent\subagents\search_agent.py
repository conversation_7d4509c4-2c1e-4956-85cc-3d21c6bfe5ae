from google.adk.agents import Agent
from google.adk.tools import google_search

# -----------------------------------------------------------------------------
# Dedicated web-search agent (built-in tool only, per ADK guardrails)
# -----------------------------------------------------------------------------

search_agent = Agent(
    name="internet_search_specialist",
    model="gemini-2.5-flash",
    description=(
        "Performs Google web searches to answer general knowledge and current-events questions."
    ),
    instruction=(
        "You are an expert internet search assistant. Upon receiving a query, "
        "use the built-in `google_search` tool to gather information and return "
        "a concise, well-sourced answer."
    ),
    tools=[google_search],  # MUST be the only tool in this agent
) 