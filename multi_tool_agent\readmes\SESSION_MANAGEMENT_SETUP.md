# Session Management Setup Guide

This guide walks you through implementing proper session management for your ADK agent to ensure conversation history is preserved across turns.

## 🎯 What We're Solving

**Problem**: The ADK agent doesn't remember previous conversation context because session IDs aren't being managed consistently between the Vercel AI SDK frontend and the ADK backend.

**Solution**: Implement proper session ID management that ensures the same session is used for the entire conversation.

## 📋 Backend Changes Made

### 1. Enhanced `chat_endpoint` Function

The endpoint now:
- ✅ <PERSON>perly extracts `session_id` from Vercel AI SDK requests (checks both `request.id` and `request.session_id`)
- ✅ Generates a unique session ID if none provided (with warning)
- ✅ Adds comprehensive logging for debugging

### 2. Enhanced `adk_agent_non_streaming_response_generator` Function

The generator now:
- ✅ **Maps frontend session IDs to ADK-generated session IDs** (since InMemorySessionService doesn't accept custom IDs)
- ✅ Uses a session mapping dictionary to maintain consistent conversation history
- ✅ Logs session creation vs. retrieval with event counts
- ✅ Includes error handling and fallback mechanisms
- ✅ Tracks session mappings for debugging

### 3. Enhanced Agent Instructions

The agent now has more directive instructions:
- ✅ **Mandatory use of `url_context_search`** when memory returns general information for BC activity queries
- ✅ **Specific query framing** for web searches (e.g., "Pedalheads biking programs New Westminster for 5 year olds current schedule registration 2025")
- ✅ **Clear order of operations**: Memory → Web Search → Clarifying Questions
- ✅ **Enhanced examples** showing when to trigger search tools

### 4. Enhanced Logging

The backend now logs:
- 📋 Session ID extraction and mapping
- 🗺️ Frontend session ID → ADK session ID mappings
- ✅ New session creation with mapping storage
- 📊 Existing session retrieval via mappings with event counts
- 🚀 ADK runner execution with mapped session IDs
- ⚠️  Warnings when mappings are stale or missing

## 🖥️ Frontend Implementation

### Required Dependencies

```bash
npm install @ai-sdk/react nanoid
```

### Basic Implementation

```tsx
import { useState, useEffect } from 'react';
import { useChat } from '@ai-sdk/react';
import { nanoid } from 'nanoid';

function ChatComponent() {
  const [currentChatId, setCurrentChatId] = useState('');

  useEffect(() => {
    // Generate consistent session ID
    const chatId = `chat_${nanoid(10)}`;
    setCurrentChatId(chatId);
  }, []);

  const { messages, input, handleInputChange, handleSubmit } = useChat({
    api: 'http://localhost:8000/api/chat',
    id: currentChatId, // ✅ Critical: This ensures session consistency!
  });

  if (!currentChatId) {
    return <p>Loading chat session...</p>;
  }

  return (
    <div>
      <div>Session: {currentChatId}</div>
      {/* ... rest of your chat UI ... */}
    </div>
  );
}
```

### Advanced Implementation with Persistence

See `frontend_session_example.tsx` for a complete implementation that includes:
- 🔄 URL-based session persistence
- 💾 localStorage session management
- 🆕 "New Chat" functionality
- 📋 Shareable chat URLs
- 🐛 Debug information

## 🧪 Testing the Implementation

### 1. Start Your Backend

```bash
python adk_server.py
```

### 2. Run the Verification Script

```bash
python test_session_management.py
```

This script will:
- ✅ Test health endpoint connectivity
- 🧪 Send a series of messages using the same session ID
- 🔍 Verify conversation history is maintained
- 📊 Test that different sessions are isolated

### 3. Manual Testing Steps

1. **Start the backend** and open browser console
2. **Send first message**: "What's the weather like tomorrow?"
   - Expected: Agent asks for city
   - Check logs: Should show "Created NEW ADK session" with mapping
3. **Send second message**: "New West BC"
   - Expected: Agent provides weather without asking for city again
   - Check logs: Should show "Found EXISTING ADK session via mapping" with events > 0
4. **Send third message**: "What biking programs are available for a 5-year-old?"
   - Expected: Agent calls retrieve_info_from_memory then url_context_search for current programs
   - Check logs: Should show both tool executions with same session
5. **Send fourth message**: "What about swimming lessons?"
   - Expected: Agent remembers context (5-year-old, New West) and searches for swimming
   - Check logs: Should show continued session history

## 🔍 Debugging Guide

### Backend Logs to Watch For

✅ **Successful Session Management**:
```
📨 CHAT REQUEST: session_id='chat_abc123', messages_count=1, user_id='user123'
✅ Created NEW ADK session: 'adk_xyz789' and mapped to frontend_session_id: 'chat_abc123'
📊 Session mapping now contains 1 entries
🚀 Running ADK agent with effective_adk_session_id: 'adk_xyz789'
```

✅ **Conversation History Working**:
```
📨 CHAT REQUEST: session_id='chat_abc123', messages_count=2, user_id='user123' 
📋 Found mapping: frontend_id='chat_abc123' -> adk_id='adk_xyz789'
✅ Found EXISTING ADK session via mapping: 'adk_xyz789' (frontend_id='chat_abc123') with 2 previous events
🚀 Running ADK agent with effective_adk_session_id: 'adk_xyz789'
```

✅ **Enhanced Tool Usage**:
```
[ADK_EVENT]: Type: ToolExecutionEvent, Tool: retrieve_info_from_memory
[ADK_EVENT]: Type: ToolExecutionEvent, Tool: url_context_search
[ADK_EVENT]: Type: ToolExecutionEvent, Tool: get_proactive_suggestions
```

❌ **Problems to Look For**:
```
⚠️  No session_id provided by frontend! Generated: 'generated_xyz789'
⚠️  Mapped ADK session_id 'old_session' not found in session service. Clearing stale mapping.
❌ Session management error: ...
🚫 Agent not calling url_context_search for activity queries
```

### Frontend Console to Check

✅ **Proper Session Management**:
```javascript
✨ Created new chat session: chat_abc123
📋 Using session_id from request.id: 'chat_abc123'
✅ Agent response received: Hi! I can help you find...
```

❌ **Frontend Issues**:
- Session ID changing between requests
- useChat `id` prop not set or changing
- Network errors in browser dev tools

### Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| No conversation history | Agent asks for info already provided | Check frontend sends consistent `id` prop |
| Session ID warnings | Backend logs "No session_id provided" | Ensure `useChat({ id: chatId })` is set |
| Different session IDs | Backend shows ID mismatches | Check ADK session service behavior |
| Context loss | Agent forgets mid-conversation | Verify same session_id across all requests |

## 🎛️ Configuration Options

### Backend Configuration

In `adk_server.py`, you can modify:

```python
# User ID strategy (currently static)
user_id = "user123"  # Consider making this dynamic

# Session ID extraction priority
# Currently checks: request.id -> request.session_id -> generated
```

### Frontend Configuration

```tsx
const { messages, input, handleInputChange, handleSubmit } = useChat({
  api: 'http://localhost:8000/api/chat',
  id: currentChatId,              // ✅ Session consistency
  keepLastMessageOnError: true,   // ✅ UX improvement
  onError: (error) => {           // ✅ Error handling
    console.error('Chat error:', error);
  },
  onFinish: (message) => {        // ✅ Response logging
    console.log('Response received:', message.content.slice(0, 100));
  }
});
```

## 🚀 Next Steps

1. **Test the implementation** using the verification script
2. **Deploy to your frontend** using the React components
3. **Monitor the logs** to ensure session consistency
4. **Consider enhancements**:
   - User authentication for multi-user support
   - Session persistence across browser restarts
   - Session cleanup for old conversations
   - Analytics on conversation patterns

## 📞 Troubleshooting

If you encounter issues:

1. **Check server health**: `curl http://localhost:8000/health`
2. **Run verification script**: `python test_session_management.py`
3. **Check browser network tab**: Ensure session_id is in request payload
4. **Monitor backend logs**: Look for session creation/retrieval messages
5. **Verify ADK session service**: Ensure InMemorySessionService is working

The key is ensuring the **same session_id flows through the entire conversation** from frontend → backend → ADK session service. 