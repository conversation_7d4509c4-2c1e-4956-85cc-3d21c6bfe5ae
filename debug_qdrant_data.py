#!/usr/bin/env python3
"""
Debug script to check what's actually in the Qdrant database
and why the filtering might be failing.
"""

import asyncio
import logging
from qdrant_client import AsyncQdrantClient
from multi_tool_agent.config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_qdrant_data():
    """Check what's in the Qdrant database."""
    try:
        # Connect to Qdrant
        client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        
        print("🔍 DEBUGGING QDRANT DATABASE")
        print("=" * 50)
        
        # Get collection info
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Collection: {AgentConfig.QDRANT_COLLECTION_NAME}")
        print(f"Total points: {collection_info.points_count}")
        print(f"Vector size: {collection_info.config.params.vectors.size}")
        
        # Try a simple scroll with no filters
        all_points, _ = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            limit=16000, # Increased to cover all points
            with_payload=True
        )
        
        burnaby_count = 0
        art_count = 0
        burnaby_art_count = 0
        age_5_count = 0
        burnaby_age_5_count = 0

        for point in all_points:
            payload = point.payload

            # Extract data from metadata if it exists
            if "metadata" in payload:
                data = payload["metadata"]
                text_field = payload.get("text", "").lower()
            else:
                data = payload
                text_field = ""

            search_text = f"{data.get('search_text', '')} {text_field}".lower()
            location = data.get('location', '').lower()
            name = data.get('name', '').lower()
            source = data.get('source', '').lower()

            # Check for Burnaby (multiple ways)
            is_burnaby = (
                'burnaby' in source or
                any(term in f"{search_text} {location} {name}" for term in ['burnaby', 'metrotown', 'shadbolt', 'edmonds'])
            )

            # Check for art activities
            is_art = any(term in f"{search_text} {name}" for term in ['art', 'creative', 'painting', 'drawing'])

            # Check for age 5 compatibility
            min_age = data.get('min_age_years')
            max_age = data.get('max_age_years')
            age_info = data.get('age_info', '').lower()

            is_age_5_compatible = False
            if min_age is not None and max_age is not None:
                is_age_5_compatible = min_age <= 5 <= max_age
            elif '5' in age_info or 'preschool' in age_info or 'toddler' in age_info:
                is_age_5_compatible = True

            if is_burnaby:
                burnaby_count += 1
            if is_art:
                art_count += 1
            if is_age_5_compatible:
                age_5_count += 1
            if is_burnaby and is_age_5_compatible:
                burnaby_age_5_count += 1
            if is_burnaby and is_art:
                burnaby_art_count += 1
                print(f"  🎯 Burnaby Art Activity: {data.get('name', 'N/A')}")
                print(f"     Location: {data.get('location', 'N/A')}")
                print(f"     Age Info: {data.get('age_info', 'N/A')}")
                print(f"     Min Age: {min_age}, Max Age: {max_age}")

            # Show some 5-year-old compatible activities
            if is_age_5_compatible and (is_burnaby or is_art):
                print(f"  👶 Age 5 Compatible: {data.get('name', 'N/A')}")
                print(f"     Location: {data.get('location', 'N/A')}")
                print(f"     Age Info: {data.get('age_info', 'N/A')}")
                print(f"     Source: {data.get('source', 'N/A')}")
                print(f"     Min Age: {min_age}, Max Age: {max_age}")
        
        print('\n[+] SUMMARY:')
        print(f"  Total activities: {len(all_points)}")
        print(f"  Burnaby activities: {burnaby_count}")
        print(f"  Art activities: {art_count}")
        print(f"  Age 5 compatible activities: {age_5_count}")
        print(f"  Burnaby + Age 5 activities: {burnaby_age_5_count}")
        print(f"  Burnaby + Art activities: {burnaby_art_count}")

        if burnaby_art_count == 0 and burnaby_age_5_count == 0:
            print("\n[-]  NO BURNABY ACTIVITIES FOR 5-YEAR-OLDS FOUND!")
            print("This explains why the search is returning no results.")
            print("The database has Burnaby activities, but none suitable for 5-year-olds.")
        elif burnaby_age_5_count > 0 and burnaby_art_count == 0:
            print("\n⚠️  BURNABY HAS 5-YEAR-OLD ACTIVITIES BUT NO ART CLASSES!")
            print("You may need to add art class data specifically.")
        else:
            print("\n[+] Found compatible activities - the issue might be in the search/filtering logic.")
        
    except Exception as e:
        print(f"❌ Error debugging Qdrant: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_qdrant_data())
