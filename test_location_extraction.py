#!/usr/bin/env python3

import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_location_extraction():
    """Test if location extraction is working correctly"""
    
    print("🔍 TESTING LOCATION EXTRACTION")
    print("=" * 40)
    
    # Test queries
    test_queries = [
        "swimming classes for 5 year olds in Burnaby",
        "Burnaby swimming lessons for kids",
        "art classes in Burnaby",
        "swimming in New Westminster",
        "classes for children"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Simple location detection
        query_lower = query.lower()
        
        detected_locations = []
        if "burnaby" in query_lower:
            detected_locations.append("Burnaby")
        if "new westminster" in query_lower or "new west" in query_lower:
            detected_locations.append("New Westminster")
        
        print(f"  Detected locations: {detected_locations}")
        
        # Check if we would create location filters
        if detected_locations:
            print(f"  ✅ Would create location filter: {detected_locations}")
        else:
            print(f"  ❌ No location filter would be created")

if __name__ == "__main__":
    test_location_extraction()
