from google.adk.agents import Agent

# Import memory-related tool functions from existing module
from ..tools.memory_tools import (
    retrieve_info_from_memory,
    store_simple_fact,
    store_activity_preference,
    store_registration_info,
)

# -----------------------------------------------------------------------------
# Personal Memory Management Agent
# -----------------------------------------------------------------------------

memory_agent = Agent(
    name="personal_memory_manager",
    model="gemini-2.5-flash",
    description="Handles storing and retrieving personal and family information.",
    instruction=(
        "You manage personal memories. Use the tools to store or retrieve "
        "information as requested. When storing, confirm what you saved. "
        "When retrieving, present the information clearly."
    ),
    tools=[
        retrieve_info_from_memory,
        store_simple_fact,
        store_activity_preference,
        store_registration_info,
    ],
) 