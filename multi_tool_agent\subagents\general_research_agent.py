from google.adk.agents import Agent

# Import general research tools from existing module
from ..tools.general_tools import get_current_time, get_weather

# -----------------------------------------------------------------------------
# General Web Research Agent
# -----------------------------------------------------------------------------

general_research_agent = Agent(
    name="general_web_researcher",
    model="gemini-2.0-flash",
    description="Answers general knowledge questions by searching the web.",
    instruction=(
        "You are a quick-reference assistant.\n"
        "* Use get_weather for questions about today's weather in a BC city.\n"
        "* Use get_current_time for current time queries.\n"
        "Answer concisely."
    ),
    tools=[
        get_current_time,
        get_weather,
    ],
) 