import logging
from typing import Dict, Any, Optional, List
from qdrant_client import models, AsyncQdrantClient
import google.generativeai as genai
try:
    from .config import AgentConfig
except ImportError:
    from config import AgentConfig
from multi_tool_agent.ingestion.data_utils import normalize_activity

logger = logging.getLogger(__name__)

def create_qdrant_filter(filters: Dict[str, Any]) -> Optional[models.Filter]:
    """
    Creates a Qdrant filter based on user query and activity criteria.
    This is now the single source of truth for DB-level filtering.
    """
    must_conditions = []

    # --- Location/Source Filtering ---
    if location_filters := filters.get("location", []):
        # Create an OR condition for multiple locations
        source_conditions = []
        for location in location_filters:
            if location.lower() == "burnaby":
                source_conditions.append(models.FieldCondition(key="source", match=models.MatchValue(value="Burnaby ActiveCommunities")))
            elif location.lower() == "new westminster":
                source_conditions.append(models.FieldCondition(key="source", match=models.MatchValue(value="New West PerfectMind")))
        
        if source_conditions:
            # If there's only one location, it's a simple must. If more, it's a must(should(...))
            if len(source_conditions) == 1:
                must_conditions.append(source_conditions[0])
            else:
                must_conditions.append(models.Filter(should=source_conditions))

    # --- Activity Criteria Filtering (Age, Category etc.) ---
    if activity_criteria := filters.get("activities", []):
        # Assuming we only process the first set of activity criteria for now
        criteria = activity_criteria[0]
        
        # Category Filter
        if categories := criteria.get("categories", []):
            must_conditions.append(
                models.FieldCondition(
                    key="category", # Use the top-level normalized key
                    match=models.MatchAny(any=categories)
                )
            )
            
        # Age Filter
        if age := criteria.get("age"):
            # Condition: user_age >= min_age_years
            must_conditions.append(
                models.FieldCondition(
                    key="min_age_years",
                    range=models.Range(lte=age) # lte = less than or equal to
                )
            )
            # Condition: user_age <= max_age_years
            must_conditions.append(
                models.FieldCondition(
                    key="max_age_years",
                    range=models.Range(gte=age) # gte = greater than or equal to
                )
            )

    # --- Multitenancy Filter ---
    if group_id := filters.get("group_id"):
        must_conditions.append(
            models.FieldCondition(key="group_id", match=models.MatchValue(value=group_id))
        )
        
    return models.Filter(must=must_conditions) if must_conditions else None

def activity_matches(activity: Dict[str, Any], activity_filter: Dict[str, Any]) -> bool:
    """
    Check if a NORMALIZED activity matches the specified filter criteria.
    Enhanced to be more flexible with age matching.
    """
    if not activity:
        return False

    # Check categories
    if "categories" in activity_filter and activity_filter["categories"]:
        if activity.get("category") not in activity_filter["categories"]:
            return False

    # Check age - enhanced to handle open-ended age ranges
    if "age" in activity_filter:
        age = activity_filter["age"]
        min_age = activity.get("min_age_years")
        max_age = activity.get("max_age_years")

        if min_age is not None:
            if age < min_age:
                return False
            if max_age is not None and age > max_age:
                return False
            return True
        else: # Lenient fallback for missing age data
            return True

    return True

async def get_activity_search(query: str, filters: Dict[str, Any], tool_context: Any) -> dict:
    """
    An improved activity search function that uses database-level filtering and robust age matching.
    """
    logger.info(f"Starting improved search for query: '{query}' with filters: {filters}")

    try:
        qdrant_client = getattr(tool_context, '_qdrant_client', None)
        if not qdrant_client:
            qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
            if tool_context:
                tool_context._qdrant_client = qdrant_client

        if not hasattr(genai, '_configured'):
            genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
            genai._configured = True

        # 1. Create Qdrant filter
        qdrant_filter = create_qdrant_filter(filters)

        # 2. Generate embedding for the query
        embedding_response = genai.embed_content(
            model=AgentConfig.EMBEDDING_MODEL, content=query, task_type="RETRIEVAL_QUERY"
        )
        query_vector = embedding_response['embedding']

        # 3. Perform search
        search_results = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=query_vector,
            query_filter=qdrant_filter,
            limit=100,
            with_payload=True,
        )

        # 4. Normalize results (no post-filtering needed since DB handles it)
        normalized_activities = [normalize_activity(point.payload) for point in search_results]

        logger.info(f"Found {len(normalized_activities)} matching activities from database.")

        return {"status": "success", "results": normalized_activities[:15]}

    except Exception as e:
        logger.error(f"Error in get_activity_search: {e}", exc_info=True)
        return {"status": "error", "message": str(e)}
