import os
from dotenv import load_dotenv

load_dotenv()

class AgentConfig:
    """Centralized configuration for the agent."""
    
    # --- Database Configuration ---
    NEO4J_URI = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    NEO4J_USER = os.environ.get('NEO4J_USER', 'neo4j')
    NEO4J_PASSWORD = os.environ.get('NEO4J_PASSWORD')
    
    # --- API Configuration ---
    GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY')
    CHUTES_API_TOKEN = os.environ.get("CHUTES_API_TOKEN")
    GROQ_API_KEY = os.environ.get("GROQ_API_KEY")
    
    # --- Qdrant Configuration ---
    QDRANT_URL = os.environ.get('QDRANT_URL', 'https://7193c1cf-a88e-4252-8b1b-ab480cdcd7d0.us-west-2-0.aws.cloud.qdrant.io:6333')
    QDRANT_API_KEY = os.environ.get('QDRANT_API_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.LAkOyhlOmDzajIb9aAgwDjEsztXp1ZWBTrU8CJgBG2c')
    QDRANT_COLLECTION_NAME = os.environ.get('QDRANT_COLLECTION_NAME', 'community_activities')
    
    # --- Agent Configuration ---
    AGENT_MODEL = "gemini-2.5-flash-lite-preview-06-17"  # Powerful, cost-effective model for synthesis
    SEARCH_AGENT_MODEL = "gemini-2.5-flash-lite-preview-06-17"  # Latest cost-effective model for internal logic
    GRAPHITI_MODEL = "gemini-2.5-flash-lite-preview-06-17"  # Latest cost-effective model for graph operations
    EMBEDDING_MODEL = "text-embedding-004"
    EMBEDDING_DIM = 768  # Dimension for text-embedding-004 model
    
    # --- Memory Configuration ---
    SAVE_QUEUE_MAX_SIZE = int(os.environ.get("SAVE_QUEUE_MAX_SIZE", 100))
    NUM_SAVE_WORKERS = int(os.environ.get("NUM_SAVE_WORKERS", 3))
    MAX_SAVE_RETRIES = int(os.environ.get("MAX_SAVE_RETRIES", 3))
    SAVE_RETRY_DELAY_SECONDS = float(os.environ.get("SAVE_RETRY_DELAY_SECONDS", 5.0))
    
    # --- Search Configuration ---
    EDGE_SEARCH_LIMIT = 7
    NODE_SEARCH_LIMIT = 3
    
    # --- Thinking Configuration ---
    THINKING_BUDGET = 384
    URL_CONTEXT_THINKING_BUDGET = 384
    
    # Thinking configuration per model
    THINKING_CONFIG = {
        "gemini-2.5-flash-lite-preview-06-17": {
            "enabled": True,
            "default_budget": 384,
            "max_budget": 768,
            "include_thoughts": True
        }
    }
    
    # --- Optimization Configuration ---
    # Prompt optimization results (baseline vs optimized)
    PROMPT_OPTIMIZATION_STATS = {
        "original_tokens": 2100,  # Estimated original prompt tokens
        "optimized_tokens": 600,  # Optimized prompt tokens  
        "reduction_percentage": 71,  # 71% reduction achieved
        "workflow_phases": 3,  # Reduced from 5 to 3 phases
        "response_templates": True,  # Template-based responses enabled
        "smart_preprocessing": True,  # Query optimization enabled
        "contextual_routing": True   # Smart tool selection enabled
    }
    
    @classmethod
    def validate(cls):
        """Validate required configuration."""
        missing = []
        
        if not cls.NEO4J_URI or not cls.NEO4J_USER or not cls.NEO4J_PASSWORD:
            missing.append("NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD")
        
        if not cls.GOOGLE_API_KEY:
            missing.append("GOOGLE_API_KEY")
            
        if not cls.CHUTES_API_TOKEN:
            missing.append("CHUTES_API_TOKEN")
            
        if not cls.GROQ_API_KEY:
            missing.append("GROQ_API_KEY")
            
        if missing:
            raise ValueError(f"Missing required environment variables: {', '.join(missing)}")
        
        return True

# Enhanced timezone mapping
TIMEZONE_MAPPING = {
    # North America
    "new york": "America/New_York",
    "los angeles": "America/Los_Angeles", 
    "chicago": "America/Chicago",
    "toronto": "America/Toronto",
    "vancouver": "America/Vancouver",
    "mexico city": "America/Mexico_City",
    "san francisco": "America/Los_Angeles",
    "seattle": "America/Los_Angeles",
    "denver": "America/Denver",
    "phoenix": "America/Phoenix",
    
    # Europe
    "london": "Europe/London",
    "paris": "Europe/Paris",
    "berlin": "Europe/Berlin",
    "rome": "Europe/Rome",
    "madrid": "Europe/Madrid",
    "amsterdam": "Europe/Amsterdam",
    "zurich": "Europe/Zurich",
    "moscow": "Europe/Moscow",
    "vienna": "Europe/Vienna",
    "stockholm": "Europe/Stockholm",
    
    # Asia
    "tokyo": "Asia/Tokyo",
    "seoul": "Asia/Seoul",
    "beijing": "Asia/Shanghai",
    "shanghai": "Asia/Shanghai",
    "mumbai": "Asia/Kolkata",
    "delhi": "Asia/Kolkata",
    "singapore": "Asia/Singapore",
    "hong kong": "Asia/Hong_Kong",
    "bangkok": "Asia/Bangkok",
    "manila": "Asia/Manila",
    
    # Australia/Oceania
    "sydney": "Australia/Sydney",
    "melbourne": "Australia/Melbourne",
    "auckland": "Pacific/Auckland",
    "perth": "Australia/Perth",
    
    # Middle East/Africa
    "dubai": "Asia/Dubai",
    "cairo": "Africa/Cairo",
    "johannesburg": "Africa/Johannesburg",
    "tel aviv": "Asia/Jerusalem",
    
    # South America
    "sao paulo": "America/Sao_Paulo",
    "buenos aires": "America/Argentina/Buenos_Aires",
    "lima": "America/Lima",
    "santiago": "America/Santiago",
}

# Cost Optimization Configuration
COST_OPTIMIZATION = {
    "model_router": {
        "enabled": True,
        "default_model": "gemini-2.5-flash-lite-preview-06-17",
        "premium_model": "gemini-2.5-flash-lite-preview-06-17",
        "complexity_thresholds": {
            "simple_max_words": 10,
            "complex_min_words": 50,
            "technical_keywords": ["analyze", "research", "explain", "compare", "implications"]
        }
    },
    "smart_thinking": {
        "enabled": True,
        "simple_greeting_budget": 32,  # 90% reduction from 1536
        "basic_question_budget": 128,  # 91% reduction from 1536  
        "research_budget": 384,        # 75% reduction from 1536
        "default_budget": 192,         # 87% reduction from 1536
        "skip_memory_for_greetings": True
    },
    "unified_memory": {
        "enabled": True,
        "batch_size": 5,
        "batch_timeout_seconds": 30,
        "extraction_confidence_threshold": 0.7
    },
    "response_caching": {
        "enabled": True,
        "max_cache_size": 100,
        "cache_ttl_seconds": 3600,  # 1 hour
        "cacheable_tools": ["get_current_time", "SearchSpecialistAgent"],
        "cache_simple_interactions": True  # Only cache thinking_budget <= 128
    },
    "optimization_tracking": {
        "enabled": True,
        "log_savings": True,
        "report_interval_minutes": 60,
        "track_thinking_token_savings": True
    }
}

# Cost tracking constants
ESTIMATED_COST_REDUCTION_TARGET = 50  # 40-60% target
TOKEN_COST_REDUCTION_MEMORY = 25  # 20-30% from unified extraction
TOKEN_COST_REDUCTION_ROUTING = 45  # 40-60% from smart routing
THINKING_TOKEN_REDUCTION_GREETINGS = 90  # 90% reduction for simple greetings (32 vs 1536) 