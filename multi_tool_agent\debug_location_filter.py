#!/usr/bin/env python3

import asyncio
import logging
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from multi_tool_agent.unified_activity_search import get_activity_search
from multi_tool_agent.filter_extraction_tool import extract_activity_filters

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

class MockToolContext:
    """Mock tool context for testing"""
    pass

async def test_burnaby_location_filter():
    """Test if location filtering works correctly for Burnaby queries"""
    
    print("🔍 Testing Burnaby location filtering...")
    
    # Test query that should return only Burnaby results
    query = "swimming classes for 5 year olds in Burnaby"
    
    # First, extract filters
    print(f"\n1. Extracting filters from query: '{query}'")
    filters = extract_activity_filters(query)
    print(f"   Extracted filters: {filters}")
    
    # Then search with filters
    print(f"\n2. Searching with filters...")
    mock_context = MockToolContext()
    result = await get_activity_search(query, mock_context, filters)
    
    print(f"\n3. Search result status: {result.get('status')}")
    print(f"   Number of results: {len(result.get('results', []))}")
    
    # Analyze the results
    if result.get('results'):
        print(f"\n4. Analyzing results for location compliance:")
        for i, activity in enumerate(result['results'][:3]):  # Check first 3
            name = activity.get('name', 'Unknown')
            venue = activity.get('venue', 'Unknown')
            source = activity.get('raw_payload', {}).get('source', 'Unknown')
            
            print(f"   Result {i+1}:")
            print(f"     Name: {name}")
            print(f"     Venue: {venue}")
            print(f"     Source: {source}")
            
            # Check if this is actually a Burnaby result
            is_burnaby = (
                'burnaby' in venue.lower() or
                'burnaby' in source.lower() or
                any(term in venue.lower() for term in ['shadbolt', 'edmonds', 'metrotown', 'brentwood'])
            )
            
            is_new_west = (
                'new west' in source.lower() or
                'təməsew̓txʷ' in venue.lower() or
                'new westminster' in venue.lower()
            )
            
            print(f"     Is Burnaby: {is_burnaby}")
            print(f"     Is New West: {is_new_west}")
            print(f"     ❌ LOCATION MISMATCH!" if is_new_west else "     ✅ Location OK")
            print()
    else:
        print("   No results to analyze")

if __name__ == "__main__":
    asyncio.run(test_burnaby_location_filter())
