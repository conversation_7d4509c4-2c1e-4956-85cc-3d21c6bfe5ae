# setup_qdrant_collection.py
# Setup Qdrant collection with proper vector configuration

import logging
import os
import sys
from pathlib import Path
from qdrant_client import QdrantClient, models

# Add the parent directory to the system path to resolve import issues
sys.path.append(str(Path(__file__).parent.parent))
try:
    from config import AgentConfig
except ImportError as e:
    raise ImportError("Could not import AgentConfig. Ensure the config module is in the parent directory.") from e

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_collection(client: QdrantClient, collection_name: str, optimization_scenario: str = "high_precision_high_speed"):
    """
    Creates the Qdrant collection with a specific schema and payload indexes.
    This function is idempotent.
    
    Args:
        client: Qdrant client instance
        collection_name: Name of the collection to create
        optimization_scenario: One of:
            - "high_precision_high_speed": Best performance, uses more RAM
            - "high_speed_low_memory": Good performance, moderate RAM usage  
            - "high_precision_low_memory": Best precision, minimal RAM usage
    """
    try:
        # Get existing collection info to see if it exists
        try:
            client.get_collection(collection_name=collection_name)
            logger.info(f"Collection '{collection_name}' already exists. Skipping creation.")
        
        except Exception:
            # If it throws an exception, it doesn't exist, so we create it.
            logger.info(f"Collection '{collection_name}' not found. Creating it now...")
            
            # Configure based on optimization scenario
            if optimization_scenario == "high_precision_high_speed":
                # Scenario 3: High Precision with High-Speed Search
                # Best performance, uses more RAM
                logger.info("Using high precision & high speed configuration (Scenario 3)")
                vectors_config = models.VectorParams(
                    size=AgentConfig.EMBEDDING_DIM, 
                    distance=models.Distance.COSINE,
                    on_disk=False,  # Vectors in RAM for maximum speed
                    hnsw_config=models.HnswConfigDiff(
                        payload_m=16,
                        m=16,  # Enable HNSW index
                        ef_construct=100
                    )
                )
                quantization_config = models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type="int8",
                        quantile=0.99,
                        always_ram=True  # Quantized vectors in RAM
                    )
                )
                
            elif optimization_scenario == "high_speed_low_memory":
                # Scenario 1: High-Speed Search with Low Memory Usage
                # Good performance, moderate RAM usage
                logger.info("Using high speed & low memory configuration (Scenario 1)")
                vectors_config = models.VectorParams(
                    size=AgentConfig.EMBEDDING_DIM, 
                    distance=models.Distance.COSINE,
                    on_disk=True,  # Original vectors on disk to save RAM
                    hnsw_config=models.HnswConfigDiff(
                        payload_m=16,
                        m=16,  # Enable HNSW index
                        ef_construct=100
                    )
                )
                quantization_config = models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type="int8",
                        quantile=0.99,
                        always_ram=True  # Quantized vectors stay in RAM for speed
                    )
                )
                
            elif optimization_scenario == "high_precision_low_memory":
                # Scenario 2: High Precision with Low Memory Usage
                # Best precision, minimal RAM usage
                logger.info("Using high precision & low memory configuration (Scenario 2)")
                vectors_config = models.VectorParams(
                    size=AgentConfig.EMBEDDING_DIM, 
                    distance=models.Distance.COSINE,
                    on_disk=True,  # Store original vectors on disk
                    hnsw_config=models.HnswConfigDiff(
                        payload_m=16,
                        m=16,
                        ef_construct=100,
                        on_disk=True  # Store HNSW graph on disk as well
                    )
                )
                quantization_config = models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type="int8",
                        quantile=0.99,
                        # No always_ram=True - let it use disk
                    )
                )
            else:
                raise ValueError(f"Unknown optimization scenario: {optimization_scenario}")
            
            client.create_collection(
                collection_name=collection_name,
                vectors_config=vectors_config,
                quantization_config=quantization_config,
            )
            logger.info(f"Collection '{collection_name}' created successfully with {optimization_scenario} configuration.")

        # --- Create payload indexes (idempotent operation) ---
        # This is the crucial step to enable efficient filtering.
        logger.info("Applying payload indexes on top-level fields...")
        
        # Index for numeric age fields for range filtering (flat only)
        client.create_payload_index(
            collection_name=collection_name,
            field_name="min_age_years",
            field_schema=models.PayloadSchemaType.INTEGER,
            wait=True,
        )
        client.create_payload_index(
            collection_name=collection_name,
            field_name="max_age_years",
            field_schema=models.PayloadSchemaType.INTEGER,
            wait=True,
        )
        # Index for keyword fields for exact matching (flat only)
        client.create_payload_index(
            collection_name=collection_name,
            field_name="category",
            field_schema=models.PayloadSchemaType.KEYWORD,
            wait=True,
        )
        client.create_payload_index(
            collection_name=collection_name,
            field_name="day_of_week",
            field_schema=models.PayloadSchemaType.KEYWORD,
            wait=True,
        )
        client.create_payload_index(
            collection_name=collection_name,
            field_name="source",
            field_schema=models.PayloadSchemaType.KEYWORD,
            wait=True,
        )
        # Index for tenant identifier to support multitenancy (flat only)
        client.create_payload_index(
            collection_name=collection_name,
            field_name="group_id",
            field_schema=models.KeywordIndexParams(
                type="keyword",
                is_tenant=True,
            ),
            wait=True,
        )
        # --- Index for full-text search fields (flat only, if present) ---
        logger.info("Applying full-text indexes...")
        client.create_payload_index(
            collection_name=collection_name,
            field_name="name",
            field_schema=models.TextIndexParams(
                type=models.TextIndexType.TEXT,
                tokenizer=models.TokenizerType.WORD,
                min_token_len=2,
                max_token_len=15,
                lowercase=True,
            )
        )
        client.create_payload_index(
            collection_name=collection_name,
            field_name="description",
            field_schema=models.TextIndexParams(
                type=models.TextIndexType.TEXT,
                tokenizer=models.TokenizerType.WORD,
                min_token_len=2,
                max_token_len=15,
                lowercase=True,
            )
        )
        
        logger.info("Payload indexes for min_age_years, max_age_years, category, day_of_week, and general_location are created or verified.")
        logger.info("Payload indexes for name, description, and category are configured for full-text search.")

    except Exception as e:
        logger.error(f"An error occurred during collection setup: {e}", exc_info=True)
        raise

def main():
    """Main function to set up the Qdrant collection."""
    try:
        client = QdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        collection_name = AgentConfig.QDRANT_COLLECTION_NAME
        
        # Default to high precision & high speed (Scenario 3)
        # Change this if you need different memory/performance trade-offs
        optimization_scenario = "high_precision_high_speed"
        
        logger.info(f"Connecting to Qdrant at {AgentConfig.QDRANT_URL} and setting up collection '{collection_name}'...")
        logger.info(f"Using optimization scenario: {optimization_scenario}")
        setup_collection(client, collection_name, optimization_scenario)
        logger.info("✅ Qdrant setup complete.")
    except Exception as e:
        logger.error(f"Failed to complete Qdrant setup: {e}")

if __name__ == "__main__":
    main()
