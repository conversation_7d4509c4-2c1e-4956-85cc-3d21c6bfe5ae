#!/usr/bin/env python3
"""
Unified Search Tool - The Single Entry Point for All Searches

This tool replaces the need for agents to choose between multiple search tools.
It automatically routes queries to the appropriate data source and handles
fallback strategies, providing a consistent interface that scales with new
cities and data sources.
"""

import logging
from typing import Dict, Any, Optional
from .smart_tool_selector import smart_tool_selector

logger = logging.getLogger(__name__)

async def unified_search(query: str, tool_context: Any) -> Dict[str, Any]:
    """
    Unified search tool that intelligently routes queries to appropriate data sources.

    This is the ONLY search tool agents should use. It handles:
    - Activity searches (local database)
    - Web searches (when local data insufficient)
    - Memory queries
    - Fallback strategies
    - Multi-city scaling

    Args:
        query: User's search query
        tool_context: Tool execution context

    Returns:
        Unified search results with source attribution
    """
    logger.info(f"Unified search called with query: '{query}'")

    try:
        # Import tools dynamically to avoid circular imports
        from .filter_extraction_tool import extract_activity_filters
        from .unified_activity_search import get_activity_search

        # Import other tools with fallbacks
        try:
            from .agent import retrieve_info_from_memory, url_context_search
        except ImportError:
            # Fallback implementations
            async def retrieve_info_from_memory(query, context):
                return {"status": "error", "message": "Memory tool not available"}

            def url_context_search(query, url=None):
                return {"status": "error", "message": "Web search tool not available"}

        # Prepare available tools for the smart selector
        available_tools = {
            'extract_activity_filters': extract_activity_filters,
            'get_activity_search': get_activity_search,
            'retrieve_info_from_memory': retrieve_info_from_memory,
            'url_context_search': url_context_search
        }
        
        # Execute with smart routing
        result = await smart_tool_selector.execute_query(
            query=query,
            tool_context=tool_context,
            available_tools=available_tools
        )
        
        # Format response for agent consumption
        if result.success:
            response = {
                'status': 'success',
                'data': result.data,
                'source': result.source.value,
                'message': result.message,
                'query': query
            }
            
            # Add specific formatting based on source
            if result.source.value == 'local_database':
                # Format local database results
                if isinstance(result.data, dict) and 'results' in result.data:
                    response['results'] = result.data['results']
                    response['search_type'] = result.data.get('search_type', 'standard')
                    response['count'] = len(result.data['results'])
            elif result.source.value == 'web_search':
                # Format web search results
                if isinstance(result.data, dict) and 'answer' in result.data:
                    response['answer'] = result.data['answer']
                    response['web_search'] = True
            
            logger.info(f"Unified search successful via {result.source.value}")
            return response
            
        else:
            logger.warning(f"Unified search failed: {result.message}")
            return {
                'status': 'error',
                'message': result.message,
                'source': result.source.value,
                'query': query,
                'suggestion': _get_failure_suggestion(query, result)
            }
            
    except Exception as e:
        logger.error(f"Unified search error: {e}", exc_info=True)
        return {
            'status': 'error',
            'message': f"Search system error: {str(e)}",
            'query': query
        }

def _get_failure_suggestion(query: str, result) -> str:
    """Generate helpful suggestions when search fails."""
    suggestions = []
    
    # Suggest broader search terms
    if 'specific' in query.lower() or 'exact' in query.lower():
        suggestions.append("Try using broader search terms")
    
    # Suggest different locations
    if any(city in query.lower() for city in ['vancouver', 'richmond', 'surrey']):
        suggestions.append("Try searching in Burnaby or New Westminster where we have more data")
    
    # Suggest different activity types
    if any(activity in query.lower() for activity in ['swimming', 'soccer', 'hockey']):
        suggestions.append("Try searching for 'classes' or 'programs' instead of specific activities")
    
    if suggestions:
        return "; ".join(suggestions)
    else:
        return "Try rephrasing your query or searching for a different activity type"

def get_search_stats() -> Dict[str, Any]:
    """Get statistics about search routing and performance."""
    return smart_tool_selector.get_stats()

# Tool metadata for ADK framework
unified_search.__name__ = "unified_search"
unified_search.__doc__ = """
Unified search tool that automatically routes queries to the best data source.

Use this tool for ANY search query - it will intelligently determine whether to:
- Search the local activity database
- Perform a web search
- Query the memory system
- Use other appropriate tools

The tool handles fallback strategies automatically, so you don't need to worry
about choosing between different search tools.
"""
