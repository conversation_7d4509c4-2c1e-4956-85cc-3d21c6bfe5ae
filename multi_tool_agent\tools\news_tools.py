"""Custom news tool to provide basic headlines for a topic.
In production this would call a real News API, but here we mock a small
response so the orchestrator has a working current-events agent.
"""

from __future__ import annotations

import logging
from typing import Dict, Any
from .decorators import tool_safe

logger = logging.getLogger(__name__)

_SAMPLE_HEADLINES = {
    "g7": [
        "G7 leaders announce coordinated climate initiative",
        "Trade and security dominate first day of G7 talks",
        "Protests erupt in host city as G7 summit opens",
    ],
    "ai": [
        "Global AI governance framework gains momentum at summit",
        "New breakthrough in energy-efficient neural chips unveiled",
        "Start-ups race to commercialise multimodal models",
    ],
}


@tool_safe()
def get_news_headlines(topic: str) -> Dict[str, Any]:
    """Return up to three recent headlines for the given topic.

    Args:
        topic: subject the user is interested in (e.g. "G7", "AI").
    """
    logger.info("Fetching news headlines for topic '%s'", topic)
    key = topic.lower().strip()
    for k in _SAMPLE_HEADLINES:
        if k in key:
            return {"status": "success", "headlines": _SAMPLE_HEADLINES[k]}
    return {"status": "success", "headlines": [f"No specific headlines found for '{topic}'."]} 