#!/usr/bin/env python3
"""
Simple test to verify that the agent uses activity search instead of web search
for Burnaby classes queries.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_agent_instructions():
    """Test that the agent instructions contain the correct tool selection rules."""
    try:
        from multi_tool_agent.agent import agent
        
        instructions = agent.instruction
        print("=== AGENT INSTRUCTIONS TEST ===")
        print(f"Agent name: {agent.name}")
        print(f"Agent model: {agent.model}")
        
        # Check if the critical tool selection rules are present
        critical_phrases = [
            "TOOL SELECTION RULES",
            "NEVER use `url_context_search` for activity searches",
            "get_activity_search",
            "extract_activity_filters"
        ]
        
        print("\n=== CHECKING FOR CRITICAL PHRASES ===")
        for phrase in critical_phrases:
            if phrase in instructions:
                print(f"✅ Found: '{phrase}'")
            else:
                print(f"❌ Missing: '{phrase}'")
        
        # Check for the Burnaby example
        if "Burnaby classes" in instructions:
            print("✅ Found: Burnaby classes example")
        else:
            print("❌ Missing: Burnaby classes example")
        
        # Print a snippet of the instructions
        print("\n=== INSTRUCTION SNIPPET ===")
        lines = instructions.split('\n')
        for i, line in enumerate(lines[:20]):  # First 20 lines
            print(f"{i+1:2d}: {line}")
        
        print(f"\n... (showing first 20 lines of {len(lines)} total lines)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent instructions: {e}")
        return False

def test_tool_availability():
    """Test that the required tools are available."""
    try:
        from multi_tool_agent.agent import agent

        print("\n=== TOOL AVAILABILITY TEST ===")

        # Get tool names
        tool_names = []
        if hasattr(agent, 'tools') and agent.tools:
            for tool in agent.tools:
                if hasattr(tool, '__name__'):
                    tool_names.append(tool.__name__)
                elif hasattr(tool, 'name'):
                    tool_names.append(tool.name)
                else:
                    tool_names.append(str(tool))

        print(f"Available tools ({len(tool_names)}):")
        for tool_name in tool_names:
            print(f"  - {tool_name}")

        # Check for required tools
        required_tools = ['extract_activity_filters', 'get_activity_search']
        for required_tool in required_tools:
            if any(required_tool in tool_name for tool_name in tool_names):
                print(f"✅ Found required tool: {required_tool}")
            else:
                print(f"❌ Missing required tool: {required_tool}")

        # Check if url_context_search is NOT in the main tools (this is what we want)
        if any('url_context_search' in tool_name for tool_name in tool_names):
            print("⚠️  url_context_search is in main tools - this could cause confusion")
        else:
            print("✅ url_context_search not in main tools - agent will prioritize local database")

        return True

    except Exception as e:
        print(f"❌ Error testing tool availability: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 TESTING BURNABY CLASSES FIX")
    print("=" * 50)
    
    success = True
    
    # Test 1: Agent instructions
    if not test_agent_instructions():
        success = False
    
    # Test 2: Tool availability
    if not test_tool_availability():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("The agent should now use activity search instead of web search for Burnaby classes.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("The fix may not be working correctly.")
    
    return success

if __name__ == "__main__":
    main()
