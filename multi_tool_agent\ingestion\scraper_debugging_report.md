# New Westminster Activity Scraper Debugging Report

## Executive Summary

Successfully debugged and fixed the New Westminster activity scraper to achieve:
- **98.3% pricing coverage** (7,843 out of 7,981 activities) - up from ~1.6% previously
- **97.5% location coverage** (7,782 out of 7,981 activities) - maintained high coverage

## Problem Analysis

### Initial Issues
1. **Pricing Information Missing**: Most activities (98.4%) had null prices
2. **Location Mapping Incomplete**: Some location IDs weren't being mapped to names

### Root Cause Discovery

#### Pricing Issue
The scraper was fetching the wrong type of detail page:
- **Class Page** (`/Class?classId=...&occurrenceDate=...`): Individual class occurrence pages - NO pricing
- **CoursesLandingPage** (`/CoursesLandingPage?courseId=...`): Course overview pages - HAS pricing

Example test results:
```
CoursesLandingPage: https://cityofnewwestminster.perfectmind.com/.../CoursesLandingPage?courseId=5acdc841-dfcc-4083-b5e9-a105e6b2965d
- Found price: $67.50 ✓

Class Page: https://cityofnewwestminster.perfectmind.com/.../Class?classId=5acdc841-dfcc-4083-b5e9-a105e6b2965d&occurrenceDate=20250806
- No price found ✗
```

#### Location Issue
The location mapping was being cleared before it could be applied to activities. Additionally, some location IDs weren't in the widget location data and needed manual override mappings.

## Solutions Implemented

### 1. Fixed URL Strategy for Pricing
Modified `_fetch_and_parse_details()` to:
```python
# First try the CoursesLandingPage URL which has pricing info
course_url = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/CoursesLandingPage?widgetId={WIDGET_ID}&courseId={class_id}"
response = await self._make_request_with_retry("get", course_url)

# If that fails, try the Class page as fallback
if not response:
    # ... fallback to Class page URL
```

### 2. Enhanced Location Mapping
- Added override mappings for unmapped location IDs
- Added facility-to-location mapping for common facilities
- Fixed the timing of when location mapping is applied

### 3. Improved Price Extraction
Implemented 4 strategies to extract prices:
1. `div.bm-price-tag` elements
2. Table cells with dollar amounts
3. Regex pattern matching for dollar amounts
4. Text content search for price patterns

## Results

### Before Fix
- Activities with pricing: 128 (1.6%)
- Activities with location: 7,780 (97.5%)
- Total activities: 7,979

### After Fix
- Activities with pricing: **7,843 (98.3%)** ✅
- Activities with location: **7,782 (97.5%)** ✅
- Total activities: 7,981

### Pricing Coverage by Activity Type
- Drop-in activities: 100% coverage (all 128 drop-ins have prices)
- Course activities: ~98% coverage (massive improvement from 0%)

### Remaining Unmapped Location IDs
Only 4 location IDs remain unmapped (affecting ~199 activities):
- `d7e273ea-21d9-4a83-ac5a-b0270d96017e`
- `37bc2e0f-0f71-4cb6-954e-548033984433`
- `30135bf3-9f95-4f13-ac4f-1387367ff178`
- `e77390ac-528a-4550-a460-a194cfa48b34`

## Key Learnings

1. **Page Type Matters**: Different page types in the PerfectMind system contain different information. Course overview pages have pricing, while individual class pages don't.

2. **Multiple Data Sources**: Location information comes from multiple sources:
   - Widget location data (primary)
   - Override mappings (for missing IDs)
   - Facility name mappings (for sub-locations)

3. **Robust Extraction**: Having multiple price extraction strategies ensures we capture pricing in various HTML formats.

4. **Async Performance**: The scraper efficiently processes ~8,000 activities in under 15 minutes using async/await patterns.

## Future Improvements

1. Investigate the 4 remaining unmapped location IDs
2. Add caching for CoursesLandingPage requests (many activities share the same course ID)
3. Consider adding more detailed error logging for the ~138 activities still missing prices