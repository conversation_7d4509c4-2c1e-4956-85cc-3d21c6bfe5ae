#!/usr/bin/env python3
"""
Helper script to configure Qdrant collection for different optimization scenarios.
"""

import sys
import os
from pathlib import Path

# Add the multi_tool_agent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'multi_tool_agent'))

from multi_tool_agent.ingestion.setup_qdrant_collection import setup_collection
from multi_tool_agent.config import AgentConfig
from qdrant_client import QdrantClient
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Configure Qdrant collection for different optimization scenarios."""
    
    scenarios = {
        "1": {
            "name": "high_precision_high_speed",
            "description": "Best performance, uses more RAM (Scenario 3)",
            "recommended_for": "Production with sufficient RAM (>8GB available)"
        },
        "2": {
            "name": "high_speed_low_memory", 
            "description": "Good performance, moderate RAM usage (Scenario 1)",
            "recommended_for": "Production with limited RAM (4-8GB available)"
        },
        "3": {
            "name": "high_precision_low_memory",
            "description": "Best precision, minimal RAM usage (Scenario 2)", 
            "recommended_for": "Memory-constrained environments (<4GB available)"
        }
    }
    
    print("=== Qdrant Optimization Scenario Configuration ===\n")
    print("Choose your optimization scenario:")
    print()
    
    for key, scenario in scenarios.items():
        print(f"{key}. {scenario['description']}")
        print(f"   Recommended for: {scenario['recommended_for']}")
        print()
    
    while True:
        choice = input("Enter your choice (1-3): ").strip()
        if choice in scenarios:
            selected_scenario = scenarios[choice]["name"]
            break
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")
    
    print(f"\nSelected: {scenarios[choice]['description']}")
    print(f"Scenario: {selected_scenario}")
    
    # Confirm before proceeding
    confirm = input("\nThis will recreate your collection. Continue? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Configuration cancelled.")
        return
    
    try:
        client = QdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        collection_name = AgentConfig.QDRANT_COLLECTION_NAME
        
        logger.info(f"Configuring collection '{collection_name}' for {selected_scenario}...")
        
        # Delete existing collection if it exists
        try:
            client.delete_collection(collection_name=collection_name)
            logger.info(f"Deleted existing collection '{collection_name}'")
        except Exception:
            logger.info(f"Collection '{collection_name}' doesn't exist, creating new one")
        
        # Create collection with selected scenario
        setup_collection(client, collection_name, selected_scenario)
        
        logger.info("✅ Collection configured successfully!")
        logger.info(f"Optimization scenario: {selected_scenario}")
        
        # Show configuration details
        collection_info = client.get_collection(collection_name=collection_name)
        config = collection_info.config.params.vectors
        
        print(f"\n=== Configuration Applied ===")
        print(f"Vectors on disk: {config.on_disk}")
        print(f"HNSW enabled: {config.hnsw_config.m > 0 if config.hnsw_config else False}")
        if config.hnsw_config:
            print(f"HNSW m: {config.hnsw_config.m}")
            print(f"HNSW on disk: {getattr(config.hnsw_config, 'on_disk', False)}")
        print(f"Quantization: {config.quantization_config.scalar.type if config.quantization_config else 'None'}")
        if config.quantization_config and hasattr(config.quantization_config.scalar, 'always_ram'):
            print(f"Quantized vectors in RAM: {config.quantization_config.scalar.always_ram}")
        
    except Exception as e:
        logger.error(f"Failed to configure collection: {e}")
        raise

if __name__ == "__main__":
    main() 