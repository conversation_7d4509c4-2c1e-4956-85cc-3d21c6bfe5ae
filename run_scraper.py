# run_scraper.py

import asyncio
import json
import logging
import sys
from pathlib import Path
# --- Use the better scraper ---
from multi_tool_agent.ingestion.comprehensive_activity_scraper import ComprehensiveActivityScraper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

__cur_dir__ = Path(__file__).parent

async def main():
    logger.info("--- Starting Comprehensive Activity Scraper ---")
    
    # Use the comprehensive scraper
    async with ComprehensiveActivityScraper() as scraper:
        all_activities = await scraper.scrape_all_activities()
    
    # Exit with an error if the scraper failed
    if not all_activities:
        logger.error("No activities were scraped. Exiting with error status.")
        sys.exit(1)

    # --- Save the comprehensive data to the unified file ---
    # The ingestion script will now read from this file.
    output_file_path = __cur_dir__ / "community_activities.jsonl"
    logger.info(f"Writing {len(all_activities)} comprehensively scraped activities to {output_file_path}.")
    
    with open(output_file_path, 'w', encoding='utf-8', errors='replace') as f:
        for activity_data in all_activities:
            # The comprehensive scraper already produces a good JSON structure.
            json.dump(activity_data, f, ensure_ascii=False)
            f.write('\n')
            
    logger.info(f"--- Scraping and processing complete. ---")

if __name__ == "__main__":
    asyncio.run(main()) 