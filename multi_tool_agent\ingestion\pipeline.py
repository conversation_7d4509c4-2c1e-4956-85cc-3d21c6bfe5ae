#!/usr/bin/env python3
"""
pipeline.py - CocoIndex Flow with Graphiti Integration for Crawl4ai Output
Using modern @cocoindex.op.function style for better async handling
"""

import asyncio
import cocoindex
import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path
from cocoindex import FlowLiveUpdaterOptions  # Import the options class

# --- Project-specific Imports ---
try:
    from .config import AgentConfig
    from .agent import graphiti_client
except ImportError:
    from config import AgentConfig
    from agent import graphiti_client

# --- Graphiti Core Imports ---
from graphiti_core.nodes import EpisodeType

# --- Configuration ---
CRAWL4AI_OUTPUT_DIR = os.path.abspath(os.getenv("CRAWL_OUTPUT_DIR", "./data/crawl4ai_output"))
COCOINDEX_DB_URL = os.getenv("COCOINDEX_DB_URL", "postgresql://cocoindex:cocoindex@localhost/cocoindex")

# --- Logging Configuration ---
flow_logger = logging.getLogger("crawl4ai_pipeline_flow")
if not flow_logger.hasHandlers():
    logging.basicConfig(
        level=os.getenv("LOG_LEVEL", "INFO").upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

# --- Modern CocoIndex Custom Operations ---

@cocoindex.op.function(cache=True, behavior_version=2)  # Metadata loading can be cached if meta file content is key
def load_associated_metadata(md_file_path_str: str) -> str:
    """Loads and parses the .meta.json file associated with an .md file."""
    import json  # Move import to top
    
    try:
        # Convert .md path to .meta.json path
        md_path = Path(md_file_path_str)
        meta_path = md_path.with_suffix('.meta.json')
        
        if meta_path.exists():
            with open(meta_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                flow_logger.info(f"Loaded metadata from {meta_path}")
                return json.dumps(metadata)
        else:
            flow_logger.warning(f"Meta file not found: {meta_path}, using defaults")
    except Exception as e:
        flow_logger.error(f"Error loading metadata from {md_file_path_str}: {e}")
    
    # Fallback to default metadata
    default_metadata = {
        "url": f"file://{md_file_path_str}",
        "title": f"Content from {Path(md_file_path_str).stem}",
        "crawl_timestamp": datetime.now(timezone.utc).isoformat(),
        "group_id": "crawled_content",
        "original_file_id": Path(md_file_path_str).stem,
        "domain": "local_file",
        "path": str(md_file_path_str),
        "source_script": "pipeline.py_fallback",
        "status": "fallback_metadata"
    }
    return json.dumps(default_metadata)

@cocoindex.op.function(cache=False)  # Helper function for location extraction
def extract_chunk_location(location_data) -> str:
    """Extract chunk location as string for UUID generation."""
    if location_data is None:
        return "full_content"
    return str(location_data)

@cocoindex.op.function(cache=False)  # Graphiti ingestion has side effects, so don't cache
def add_to_graphiti(
    episode_content: str,
    page_metadata_json: str,
    parent_file_id: str,
    chunk_location_info: str = ""
) -> str:
    """Adds content as an episode to Graphiti knowledge graph with robust connection handling."""
    
    print(f"[DEBUG] add_to_graphiti called! Content length: {len(episode_content)}, parent_file_id: {parent_file_id}, chunk: {chunk_location_info}")
    
    # Parse metadata from JSON string
    import json
    import asyncio
    import hashlib
    try:
        page_metadata = json.loads(page_metadata_json)
    except json.JSONDecodeError as e:
        flow_logger.error(f"Failed to parse metadata JSON: {e}")
        # Fallback to default metadata
        page_metadata = {
            "url": "file://local_crawl",
            "title": f"Content from {parent_file_id}",
            "crawl_timestamp": datetime.now(timezone.utc).isoformat(),
            "group_id": "crawled_content",
            "original_file_id": parent_file_id,
            "domain": "local",
            "path": "/",
            "source_script": "pipeline.py",
            "status": "generated_metadata"
        }
    
    # Validate inputs
    if not episode_content or len(episode_content.strip()) < 10:
        flow_logger.warning("Content too short for Graphiti processing")
        return json.dumps({
            "status": "skipped",
            "reason": "Content too short",
            "episode_uuid": None,
            "summary": "Skipped: Content too short"
        })
    
    # Extract metadata
    episode_name = page_metadata.get("title", f"Content from {parent_file_id}")
    source_url = page_metadata.get("url", f"file://{parent_file_id}")
    group_id = page_metadata.get("group_id", "crawled_content")
    page_stable_id = page_metadata.get("original_file_id", parent_file_id)
    
    # Parse reference time
    try:
        crawl_timestamp_str = page_metadata.get("crawl_timestamp")
        if crawl_timestamp_str:
            reference_time = datetime.fromisoformat(crawl_timestamp_str.replace('Z', '+00:00'))
            if reference_time.tzinfo is None:
                reference_time = reference_time.replace(tzinfo=timezone.utc)
        else:
            reference_time = datetime.now(timezone.utc)
    except ValueError as e:
        flow_logger.warning(f"Invalid crawl_timestamp format: {crawl_timestamp_str}. Using current time. Error: {e}")
        reference_time = datetime.now(timezone.utc)
    
    # Create unique episode UUID for chunks with proper chunk identification
    episode_uuid_for_graphiti = None
    if page_stable_id:
        # Always generate a stable UUID for idempotency
        if chunk_location_info and chunk_location_info not in ["chunk", "", "full_content"]:
            # Sanitize chunk location info for UUID (remove brackets, commas, spaces)
            safe_chunk_info = str(chunk_location_info).replace('[', '').replace(']', '').replace(',', '_').replace(' ', '')
            episode_uuid_for_graphiti = f"{page_stable_id}_{safe_chunk_info}"
            episode_name = f"{episode_name} (chunk {safe_chunk_info})"
        else:
            # Single chunk/whole file or generic chunk - use content hash for uniqueness
            content_hash = hashlib.md5(episode_content[:500].encode()).hexdigest()[:8]
            episode_uuid_for_graphiti = f"{page_stable_id}_{content_hash}"
    
    flow_logger.info(f"Processing episode '{episode_name[:50]}...' for Graphiti (Group: {group_id}, UUID: {episode_uuid_for_graphiti})")
    
    # Check if Graphiti client is available
    if not graphiti_client:
        error_msg = "Graphiti client not available"
        flow_logger.error(error_msg)
        return json.dumps({
            "status": "error",
            "error_message": error_msg,
            "episode_uuid": None,
            "summary": f"Error: {error_msg}"
        })
    
    # Create async function with improved connection handling
    async def run_graphiti_processing():
        """Run Graphiti processing with robust connection management."""
        # Ensure we have a fresh client reference
        from agent import graphiti_client as client
        
        if not client:
            raise Exception("Graphiti client not available in async context")
        
        # Prepare kwargs for add_episode
        episode_kwargs = {
            'name': episode_name,
            'episode_body': episode_content,
            'source': EpisodeType.text,
            'source_description': f"Web crawl: {source_url}",
            'reference_time': reference_time,
            'group_id': group_id
        }
        
        # Strategy: Handle constraint violation gracefully for true idempotency
        # Graphiti has constraint on (name, group_id), so duplicate creations fail
        try:
            # Attempt to create episode
            flow_logger.info(f"Creating episode: {episode_name}")
            results = await client.add_episode(**episode_kwargs)
            
            # Extract results
            nodes_count = len(results.nodes) if hasattr(results, 'nodes') and results.nodes else 0
            edges_count = len(results.edges) if hasattr(results, 'edges') and results.edges else 0
            episode_uuid_result = results.episode.uuid if hasattr(results, 'episode') and results.episode else episode_uuid_for_graphiti
            
            summary = f"Episode: {episode_uuid_result}, Nodes: {nodes_count}, Edges: {edges_count}"
            flow_logger.info(f"Graphiti processing completed: {summary}")
            
            return json.dumps({
                "status": "success",
                "episode_uuid": episode_uuid_result,
                "nodes_count": nodes_count,
                "edges_count": edges_count,
                "summary": summary
            })
            
        except Exception as creation_error:
            error_msg = str(creation_error)
            
            # Check if this is a constraint violation (duplicate episode)
            if "ConstraintValidationFailed" in error_msg and "already exists" in error_msg:
                # Episode already exists - this is expected for idempotency
                flow_logger.info(f"Episode already exists (idempotent): {episode_name}")
                
                # Don't query for existing UUID to avoid session conflicts
                # Just return success with the stable UUID we generated
                return json.dumps({
                    "status": "success",
                    "episode_uuid": episode_uuid_for_graphiti or "existing",
                    "nodes_count": 0,  # No new nodes created
                    "edges_count": 0,  # No new edges created
                    "summary": f"Existing episode (idempotent): {episode_uuid_for_graphiti or 'found'}"
                })
            
            else:
                # Different error - re-raise for retry logic
                raise creation_error
    
    # Execute with asyncio.run in a clean way for CocoIndex
    try:
        # Check if we're already in an async context
        try:
            loop = asyncio.get_running_loop()
            # We're in an async context, use thread executor
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, run_graphiti_processing())
                return future.result(timeout=120)  # 2 minute timeout
        except RuntimeError:
            # No running loop, we can use asyncio.run directly
            return asyncio.run(run_graphiti_processing())
            
    except Exception as e:
        error_msg = f"Graphiti error for {episode_name[:30]}...: {str(e)}"
        flow_logger.error(error_msg, exc_info=True)
        return json.dumps({
            "status": "error",
            "error_message": str(e),
            "episode_uuid": episode_uuid_for_graphiti,
            "summary": f"Error: {str(e)[:100]}"
        })

# --- CocoIndex Flow Definition ---

@cocoindex.flow_def(name="Crawl4aiToGraphiti")
def crawl4ai_to_graphiti_flow(flow_builder: cocoindex.FlowBuilder, data_scope: cocoindex.DataScope):
    """
    CocoIndex flow to process crawl4ai output and send to Graphiti.
    
    Flow:
    1. Monitor CRAWL4AI_OUTPUT_DIR for .md files
    2. Load associated .meta.json metadata
    3. Optionally chunk large content
    4. Send each chunk/page to Graphiti
    """
    flow_logger.info(f"Defining Crawl4aiToGraphiti flow, monitoring: {CRAWL4AI_OUTPUT_DIR}")
    
    # Ensure output directory exists
    if not os.path.exists(CRAWL4AI_OUTPUT_DIR):
        os.makedirs(CRAWL4AI_OUTPUT_DIR, exist_ok=True)
        flow_logger.info(f"Created CRAWL4AI_OUTPUT_DIR: {CRAWL4AI_OUTPUT_DIR}")
    
    # 1. Source: Markdown files from crawl4ai output directory
    data_scope["crawled_md_files"] = flow_builder.add_source(
        cocoindex.sources.LocalFile(
            path=CRAWL4AI_OUTPUT_DIR,
            included_patterns=["*.md"]
        )
    )
    
    # Process each Markdown file
    with data_scope["crawled_md_files"].row() as md_file:
        # 2. Load metadata using the file path from LocalFile source
        # LocalFile source provides both 'content' and 'filename' fields
        page_metadata = md_file["filename"].transform(load_associated_metadata)
        
        # 3. Use the content directly (LocalFile returns strings by default)
        file_content = md_file["content"]
        
        # 4. Optional: Chunk the Markdown content if pages are very large
        content_chunks = file_content.transform(
            cocoindex.functions.SplitRecursively(),
            language="markdown",
            chunk_size=4000,  # Adjust based on Graphiti's LLM context limits
            chunk_overlap=400
        )
        
        # Process each chunk
        with content_chunks.row() as text_chunk:
            # 5. Send to Graphiti - extract chunk location properly from SplitRecursively
            # SplitRecursively typically provides location as [start, end] offsets
            text_chunk["chunk_location_str"] = text_chunk["location"].transform(
                extract_chunk_location
            )
            
            text_chunk["graphiti_ingestion_result"] = text_chunk["text"].transform(
                add_to_graphiti,
                page_metadata_json=page_metadata,
                parent_file_id=md_file["filename"],
                chunk_location_info=text_chunk["chunk_location_str"]
            )
            
            # Results are stored as JSON string in graphiti_ingestion_result

# --- Pipeline Execution Functions ---

async def setup_graphiti_database():
    """One-time setup for Graphiti indices and constraints."""
    try:
        if graphiti_client:
            flow_logger.info("Setting up Graphiti database indices and constraints...")
            await graphiti_client.build_indices_and_constraints()
            flow_logger.info("Graphiti database setup completed successfully")
        else:
            flow_logger.error("Graphiti client not available for setup")
            return False
        return True
    except Exception as e:
        flow_logger.error(f"Error setting up Graphiti database: {e}", exc_info=True)
        return False

async def run_pipeline_once():
    """Run the CocoIndex pipeline once (batch mode)."""
    flow_logger.info("Starting CocoIndex pipeline: Crawl4aiToGraphiti")
    
    # Validate environment
    if not COCOINDEX_DB_URL:
        flow_logger.error("COCOINDEX_DB_URL environment variable is not set")
        return False
    
    if not graphiti_client:
        flow_logger.error("Graphiti client not available")
        return False
    
    try:
        # Initialize CocoIndex
        flow_logger.info("Initializing CocoIndex for Crawl4aiToGraphiti flow")
        cocoindex.init(
            settings=cocoindex.Settings(
                app_namespace="crawl4ai_pipeline",
                database=cocoindex.storages.DatabaseConnectionSpec(url=COCOINDEX_DB_URL)
            )
        )
        flow_logger.info(f"CocoIndex initialized with DB: {COCOINDEX_DB_URL}")
        
        # Run the flow using the correct API
        flow_logger.info("Starting CocoIndex flow run: Crawl4aiToGraphiti")
        
        # Try the available update_all_flows_async method
        try:
            await cocoindex.update_all_flows_async(FlowLiveUpdaterOptions())
            flow_logger.info("CocoIndex pipeline completed successfully using update_all_flows_async")
            return True
        except Exception as e:
            flow_logger.warning(f"update_all_flows_async failed: {e}")
            
            # Fallback: try to access the flow object directly
            try:
                # The flow function might be accessible directly after flow_def registration
                flow_logger.info("Attempting direct flow object access...")
                if hasattr(crawl4ai_to_graphiti_flow, 'update'):
                    await crawl4ai_to_graphiti_flow.update()
                    flow_logger.info("CocoIndex pipeline completed successfully using direct flow access")
                    return True
                else:
                    flow_logger.error("Flow object does not have update method")
                    return False
            except Exception as fallback_error:
                flow_logger.error(f"Direct flow access failed: {fallback_error}")
                raise e  # Re-raise the original error
        
    except Exception as e:
        flow_logger.error(f"Pipeline execution error: {e}", exc_info=True)
        return False
        
    finally:
        # Clean up Graphiti connection
        if graphiti_client:
            try:
                await graphiti_client.close()
                flow_logger.info("Graphiti connection closed")
            except Exception as e:
                flow_logger.warning(f"Error closing Graphiti connection: {e}")

async def run_pipeline_continuous():
    """Show instructions for running the CocoIndex pipeline in continuous mode."""
    flow_logger.info("For continuous pipeline processing, use CocoIndex server:")
    
    print("\n" + "="*60)
    print("🔄 Continuous Pipeline Mode")
    print("="*60)
    print("\n1. Start CocoIndex server:")
    print("   cocoindex server start")
    print("\n2. Run the flow continuously:")
    print("   cocoindex server run-flow Crawl4aiToGraphiti")
    print("\n3. Monitor with CocoIndex web UI (if available)")
    print("\nAlternatively, set up a scheduler to run this script periodically.")

def print_usage():
    """Print usage information."""
    print("🔄 Crawl4ai to Graphiti Pipeline")
    print("=" * 50)
    print(f"Monitoring directory: {CRAWL4AI_OUTPUT_DIR}")
    print(f"CocoIndex DB: {COCOINDEX_DB_URL}")
    print("")
    print("Usage:")
    print("  python pipeline.py                 # Run once (batch mode)")
    print("  python pipeline.py --setup         # Setup Graphiti (one-time)")
    print("  python pipeline.py --continuous    # Show continuous mode instructions")
    print("")
    print("Environment Variables:")
    print("  CRAWL_OUTPUT_DIR     - Directory to monitor for .md files")
    print("  COCOINDEX_DB_URL     - PostgreSQL URL for CocoIndex")
    print("  NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD - Neo4j for Graphiti")
    print("  GOOGLE_API_KEY       - Google API key for Graphiti")

async def main():
    """Main entry point."""
    import sys
    
    # Validate configuration
    try:
        AgentConfig.validate()
    except Exception as e:
        flow_logger.error(f"Configuration validation failed: {e}")
        print_usage()
        return
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--setup":
            flow_logger.info("Setting up Graphiti database...")
            success = await setup_graphiti_database()
            if success:
                print("✅ Graphiti setup completed successfully")
            else:
                print("❌ Graphiti setup failed")
            return
            
        elif sys.argv[1] == "--continuous":
            await run_pipeline_continuous()
            return
            
        elif sys.argv[1] == "--help":
            print_usage()
            return
    
    # Default: run once
    print_usage()
    print("\nStarting batch processing...")
    success = await run_pipeline_once()
    
    if success:
        print("✅ Pipeline completed successfully")
        print(f"   Processed files from: {CRAWL4AI_OUTPUT_DIR}")
        print("   Knowledge added to Graphiti")
    else:
        print("❌ Pipeline failed")
        print("   Check logs for details")

if __name__ == "__main__":
    asyncio.run(main()) 