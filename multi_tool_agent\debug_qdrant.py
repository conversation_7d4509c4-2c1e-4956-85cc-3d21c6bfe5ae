#!/usr/bin/env python3
"""Quick debugging helper to inspect documents in the Qdrant `community_activities`
collection and try a manual filter. Run:

    python multi_tool_agent/debug_qdrant.py

Adjust CATEGORY_VALUE or other constants below to experiment.
"""

import asyncio
import pprint

from qdrant_client import Async<PERSON>drant<PERSON>lient, models
from .config import AgentConfig

CATEGORY_VALUE = "art"  # <-- tweak this to what you expect in category field
AGE = 5                 # age to test in min/max filters
COLLECTION = "community_activities"

async def get_collection_info(client: AsyncQdrantClient, collection_name: str):
    """Prints information about the collection."""
    try:
        collection_info = await client.get_collection(collection_name=collection_name)
        print("--- Collection Info ---")
        print(f"Vectors count: {collection_info.vectors_count}")
        print(f"Points count: {collection_info.points_count}")
        print("-" * 20)
    except Exception as e:
        print(f"Could not get collection info: {e}")

async def query_for_age_and_text(client: AsyncQdrantClient, collection_name: str, target_age: int, search_text: str):
    """
    Queries for activities suitable for a specific age and matching a keyword.
    """
    print(f"\n--- Querying for age '{target_age}' and text '{search_text}' ---")
    try:
        q_filter = models.Filter(
            must=[
                models.FieldCondition(key="min_age_years", range=models.Range(lte=target_age)),
                models.FieldCondition(key="max_age_years", range=models.Range(gte=target_age)),
                models.Filter(
                    should=[
                        models.FieldCondition(key="name", match=models.MatchText(text=search_text)),
                        models.FieldCondition(key="description", match=models.MatchText(text=search_text)),
                        models.FieldCondition(key="category", match=models.MatchText(text=search_text)),
                    ]
                )
            ]
        )

        records, _ = await client.scroll(
            collection_name=collection_name,
            scroll_filter=q_filter,
            limit=25,
            with_payload=True
        )

        print(f"Found {len(records)} records matching the criteria.")
        if not records:
            return

        for i, record in enumerate(records):
            payload = record.payload
            print(f"\n--- Result {i+1} ---")
            print(f"  Name:     {payload.get('name')}")
            print(f"  Ages:     {payload.get('age_info')}")
            print(f"  Category: {payload.get('category')}")
            print(f"  Location: {payload.get('general_location')}")
            print(f"  Time:     {payload.get('start_time')}")
            print(f"  Date:     {payload.get('start_date')}")

    except Exception as e:
        print(f"An error occurred during the query: {e}")


async def main():
    """Establishes a client and runs the debug queries."""
    client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    collection_name = AgentConfig.QDRANT_COLLECTION_NAME
    
    await get_collection_info(client, collection_name)
    await query_for_age_and_text(client, collection_name, 5, "gym")
    await query_for_age_and_text(client, collection_name, 5, "gymnastics")
    await query_for_age_and_text(client, collection_name, 5, "trampoline")


if __name__ == "__main__":
    asyncio.run(main()) 