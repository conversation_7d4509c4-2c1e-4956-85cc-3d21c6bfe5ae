"""
Minimal schema definitions for Graphiti memory operations.
These replace verbose Pydantic models to reduce token usage by 30-40%.
"""

# Minimal entity schemas - only essential fields
ENTITY_SCHEMAS = {
    "Person": ["name", "age", "location"],
    "Location": ["city", "country"], 
    "Interest": ["topic", "level"],
    "Knowledge": ["topic", "source", "content_type"],
    "ResearchSession": ["query_intent", "timestamp"]
}

# Minimal edge schemas - only essential fields
EDGE_SCHEMAS = {
    "HAS_CHILD": ["relationship_type"],
    "LIVES_IN": ["residence_type"],
    "INTERESTED_IN": ["level_of_interest"],
    "RESEARCHED_FOR": ["context", "relevance_score"],
    "RELATED_TO_INTEREST": ["relevance", "connection_type"],
    "BASED_ON_RESEARCH": ["extraction_method", "confidence"]
}

# Simplified edge type mapping
EDGE_TYPE_MAP = {
    ("Person", "Person"): ["HAS_CHILD"],
    ("Person", "Location"): ["LIVES_IN"], 
    ("Person", "Interest"): ["INTERESTED_IN"],
    ("Person", "Knowledge"): ["RESEARCHED_FOR"],
    ("Person", "ResearchSession"): ["RESEARCHED_FOR"],
    ("Knowledge", "Interest"): ["RELATED_TO_INTEREST"],
    ("Knowledge", "ResearchSession"): ["BASED_ON_RESEARCH"]
}

# Schema-less storage for non-critical data
SCHEMALESS_FIELDS = ["details", "description", "notes", "metadata"]

class SchemaOptimizer:
    """Manages minimal schema usage to reduce token consumption."""
    
    @staticmethod
    def get_minimal_schema(entity_type: str) -> dict:
        """Return minimal schema for entity type."""
        if entity_type in ENTITY_SCHEMAS:
            return {
                "type": entity_type,
                "fields": ENTITY_SCHEMAS[entity_type]
            }
        return {"type": "Generic", "fields": ["name", "value"]}
    
    @staticmethod
    def optimize_for_extraction(data: dict) -> dict:
        """Remove verbose fields before sending to LLM."""
        optimized = {}
        for key, value in data.items():
            if key not in SCHEMALESS_FIELDS and len(str(value)) < 100:
                optimized[key] = value
        return optimized 