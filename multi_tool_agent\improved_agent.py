
import logging
from google.adk.agents import Agent
from google.adk.tools import agent_tool
from .config import AgentConfig
from .improved_activity_search import improved_activity_search
from .filter_extraction_tool import extract_activity_filters

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

improved_agent = Agent(
    name="improved_bc_parent_activity_assistant",
    model=AgentConfig.AGENT_MODEL,
    description="An improved AI assistant for finding children's activities in BC.",
    instruction=(
        "You are an expert assistant for parents in British Columbia. "
        "Your primary function is to help them find children's activities using our local activity database. "
        "Always use the `extract_activity_filters` tool first, then the `improved_activity_search` tool."
    ),
    tools=[
        extract_activity_filters,
        improved_activity_search,
    ],
)

# Expose for Google ADK
root_agent = improved_agent
