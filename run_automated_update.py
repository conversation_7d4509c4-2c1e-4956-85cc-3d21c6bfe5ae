import asyncio
import json
import logging
import subprocess
import sys
from pathlib import Path

import httpx

# --- Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger("AutoUpdate")

# The URL of the page we check for updates.
# This should be a stable entry point to the content.
TARGET_URL = "https://cityofnewwestminster.perfectmind.com/23693/Clients/BookMe4?widgetId=50a33660-b4f7-44d9-9256-e10effec8641"

# File to store the state (ETag or Last-Modified header).
STATE_FILE = Path(__file__).parent / "update_state.json"

# Scripts to run when an update is detected.
SCRAPER_SCRIPT = Path(__file__).parent / "run_scraper.py"
INGESTION_SCRIPT = Path(__file__).parent / "update_qdrant_activities.py"
# --- End Configuration ---

def run_script(script_path: Path):
    """Executes a given Python script and streams its output in real-time."""
    if not script_path.exists():
        logger.error(f"Script not found: {script_path}")
        return False
    
    logger.info(f"--- Running script: {script_path.name} ---")
    try:
        # Use Popen to stream output in real-time.
        process = subprocess.Popen(
            [sys.executable, str(script_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # Redirect stderr to stdout
            text=True,
            encoding='utf-8',
            errors='replace',
            bufsize=1  # Line-buffered
        )

        # Read and print output line by line as it comes in.
        for line in iter(process.stdout.readline, ''):
            # Print the output from the script, removing extra newlines
            logger.info(f"  > {line.strip()}")
        
        process.stdout.close()
        return_code = process.wait()

        if return_code != 0:
            logger.error(f"Script {script_path.name} failed with return code {return_code}")
            return False

        logger.info(f"--- Finished script: {script_path.name} (Success) ---")
        return True
        
    except Exception as e:
        logger.error(f"An unexpected error occurred while running {script_path.name}: {e}")
        return False

async def check_for_updates():
    """
    Checks the target URL for content changes using HTTP headers.
    If a change is detected, it orchestrates the scraping and ingestion process.
    """
    logger.info(f"Checking for updates at: {TARGET_URL}")
    
    # 1. Load the last known state from the state file.
    last_state = {}
    if STATE_FILE.exists():
        with open(STATE_FILE, 'r') as f:
            try:
                last_state = json.load(f)
                logger.info(f"Loaded last known state: {last_state}")
            except json.JSONDecodeError:
                logger.warning("Could not decode state file. Assuming no prior state.")

    # 2. Prepare headers for a conditional request.
    headers = {
        'User-Agent': 'City-Activity-Update-Checker/1.0'
    }
    if 'etag' in last_state:
        headers['If-None-Match'] = last_state['etag']
    if 'last_modified' in last_state:
        headers['If-Modified-Since'] = last_state['last_modified']
        
    # 3. Make the request to the server.
    try:
        async with httpx.AsyncClient(follow_redirects=True, timeout=30.0) as client:
            response = await client.head(TARGET_URL, headers=headers)
            
            # 4a. Check if the content is unchanged.
            if response.status_code == 304:
                logger.info("✅ No changes detected (304 Not Modified). No action needed.")
                return

            response.raise_for_status() # Check for other errors like 4xx or 5xx.
            
            # 4b. If we get a 200 OK, content has likely changed.
            logger.info(f"🔥 Change detected (Status: {response.status_code}). New headers: {response.headers}")

            # 5. Run the update workflow.
            logger.info("Starting the data update workflow...")
            
            if not run_script(SCRAPER_SCRIPT):
                logger.error("Scraper script failed. Aborting workflow.")
                return

            if not run_script(INGESTION_SCRIPT):
                logger.error("Ingestion script failed. The database may be out of sync.")
                return
            
            # 6. Save the new state.
            new_state = {}
            if 'etag' in response.headers:
                new_state['etag'] = response.headers['etag']
            if 'last-modified' in response.headers:
                new_state['last_modified'] = response.headers['last-modified']

            if new_state:
                logger.info(f"Saving new state to {STATE_FILE}: {new_state}")
                with open(STATE_FILE, 'w') as f:
                    json.dump(new_state, f, indent=4)
                logger.info("🎉 Workflow complete. Database is now up-to-date.")
            else:
                logger.warning("Server did not provide ETag or Last-Modified headers. Cannot save state for next check.")

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during check: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(check_for_updates()) 