#!/usr/bin/env python3

import logging
import re
import json
from typing import Dict, List, Optional, Any
from google.adk.tools.tool_context import ToolContext
import google.generativeai as genai
from collections import defaultdict
from .model_router import model_router, TaskType

logger = logging.getLogger(__name__)

def extract_activity_filters(query: str, tool_context: ToolContext) -> Dict[str, Any]:
    """
    Extract structured filters from natural language activity queries using an LLM-based approach.
    
    Args:
        query: Natural language query about activities
        tool_context: Tool execution context
        
    Returns:
        Dict with extracted filters
    """
    logger.info(f"Extracting filters from query: '{query}'")
    
    try:
        # Use LLM-based extraction for all filters
        filters = _get_llm_extracted_filters(query)
        
        if not filters:
            logger.warning("LLM extraction failed, returning empty filters")
            return {}
        
        logger.info(f"Extracted filters: {filters}")
        return filters
        
    except Exception as e:
        logger.error(f"Filter extraction failed: {e}", exc_info=True)
        return {}

def _extract_categories(query: str) -> List[str]:
    """Extract activity categories from query."""
    categories = []
    
    # Category mapping with multiple keywords per category
    # CRITICAL FIX: Removed "general" category auto-detection for generic terms
    # This was filtering results to only "General Interest" activities which lack age-appropriate options
    category_keywords = {
        "aquatics": ["swim", "swimming", "pool", "water", "aquatic"],
        "fitness": ["fitness", "gym", "workout", "exercise", "strength"], 
        "sports": ["sport", "soccer", "football", "basketball", "hockey", "tennis"],
        "arts": ["art", "arts", "painting", "drawing", "music", "dance", "drama"],
        "biking": ["bike", "biking", "cycling", "bicycle", "pedalheads"]
        # Removed: "general": ["activity", "program", "class", "lesson"]
    }
    
    for category, keywords in category_keywords.items():
        if any(keyword in query for keyword in keywords):
            categories.append(category)
    
    return list(set(categories))  # Remove duplicates

def _extract_days_of_week(query: str) -> List[str]:
    """Extract days of week from query."""
    days = []
    
    # Day patterns
    day_patterns = {
        "monday": ["monday", "mon", "mondays"],
        "tuesday": ["tuesday", "tue", "tuesdays"],
        "wednesday": ["wednesday", "wed", "wednesdays"],
        "thursday": ["thursday", "thu", "thursdays"],
        "friday": ["friday", "fri", "fridays"],
        "saturday": ["saturday", "sat", "saturdays"],
        "sunday": ["sunday", "sun", "sundays"]
    }
    
    for day, patterns in day_patterns.items():
        if any(pattern in query for pattern in patterns):
            days.append(day)
    
    # Handle special day groups
    if any(term in query for term in ["weekend", "weekends"]):
        days.extend(["saturday", "sunday"])
    
    if any(term in query for term in ["weekday", "weekdays"]):
        days.extend(["monday", "tuesday", "wednesday", "thursday", "friday"])
    
    return list(set(days))  # Remove duplicates

def _extract_time_constraints(query: str) -> Dict[str, Any]:
    """Extract time period constraints from query."""
    time_constraints = {}
    periods = []
    
    # Time period patterns
    if any(term in query for term in ["morning", "am", "a.m."]):
        periods.append("morning")
    
    if any(term in query for term in ["afternoon", "pm", "p.m.", "after school"]):
        periods.append("afternoon")
    
    if any(term in query for term in ["evening", "night"]):
        periods.append("evening")
    
    if periods:
        time_constraints["periods"] = periods
    
    # Extract specific times using regex
    time_pattern = r'(\d{1,2}):?(\d{2})?\s*(am|pm|a\.m\.|p\.m\.)'
    matches = re.findall(time_pattern, query, re.IGNORECASE)
    if matches:
        times = []
        for hour, minute, period in matches:
            minute = minute or "00"
            times.append(f"{hour}:{minute} {period.upper()}")
        time_constraints["specific_times"] = times
    
    return time_constraints

def _extract_age_constraints(query: str) -> Dict[str, Any]:
    """Extract age constraints from query."""
    age_constraints = {}
    
    # Age pattern matching
    age_patterns = [
        r'(\d+)\s*year\s*old',
        r'(\d+)\s*yr\s*old',
        r'age\s*(\d+)',
        r'(\d+)\s*years?\s*old',
        r'aged\s*(\d+)', # Catches "aged 5" or "aged 3"
        r'(\d+)\s*and\s*(\d+)', # Catches "3 and 6"
        r'(\d+)-year-olds' # <--- Added to match "5-year-olds"
    ]
    
    ages = []
    for pattern in age_patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        for match in matches:
            if isinstance(match, tuple): # Handles the '(\d+)\s*and\s*(\d+)' case
                ages.extend([int(age) for age in match])
            else:
                ages.append(int(match))
    
    if ages:
        # If specific ages found, create range
        min_age = min(ages)
        max_age = max(ages)
        age_constraints["min_age"] = min_age
        age_constraints["max_age"] = max_age
        age_constraints["specific_ages"] = sorted(list(set(ages)))
    
    # Age group patterns
    if any(term in query for term in ["toddler", "toddlers"]):
        age_constraints.update({"min_age": 1, "max_age": 3, "group": "toddler"})
    elif any(term in query for term in ["preschool", "preschooler"]):
        age_constraints.update({"min_age": 3, "max_age": 5, "group": "preschool"})
    elif any(term in query for term in ["teen", "teens", "teenager"]):
        age_constraints.update({"min_age": 13, "max_age": 18, "group": "teen"})
    elif any(term in query for term in ["adult", "adults"]):
        age_constraints.update({"min_age": 18, "max_age": 99, "group": "adult"})
    
    return age_constraints

def _extract_locations(query: str) -> List[str]:
    """Extract location preferences from query."""
    locations = []
    
    # BC location patterns
    bc_locations = [
        "vancouver", "burnaby", "richmond", "surrey", "langley", "coquitlam",
        "new westminster", "north vancouver", "west vancouver", "delta",
        "white rock", "port moody", "port coquitlam", "maple ridge",
        "downtown", "uptown", "south vancouver", "east vancouver",
        "north burnaby", "south burnaby", "steveston", "metrotown",
        "edmonds"
    ]
    
    # List of centers with city information for precise location matching
    centers_list = [
        {"city": "Burnaby", "id": "46", "desc": "Armstrong Elementary School"},
        {"city": "Burnaby", "id": "47", "desc": "Aubrey Elementary School"},
        {"city": "Burnaby", "id": "48", "desc": "Bonsor Recreation Complex"},
        {"city": "Burnaby", "id": "49", "desc": "Brantford Elementary School"},
        {"city": "Burnaby", "id": "50", "desc": "Brentwood Park Elementary School"},
        {"city": "Burnaby", "id": "51", "desc": "Buckingham Elementary School"},
        {"city": "Burnaby", "id": "52", "desc": "Burnaby Central Secondary School"},
        {"city": "Burnaby", "id": "53", "desc": "Burnaby Heights Baptist Church"},
        {"city": "Burnaby", "id": "54", "desc": "Burnaby Lake Sports Complex West"},
        {"city": "Burnaby", "id": "55", "desc": "Burnaby Mountain Secondary School"},
        {"city": "Burnaby", "id": "56", "desc": "Burnaby North Secondary School"},
        {"city": "Burnaby", "id": "57", "desc": "Burnaby South Secondary School"},
        {"city": "Burnaby", "id": "58", "desc": "Byrne Creek Community School"},
        {"city": "Burnaby", "id": "59", "desc": "Cameron Elementary School"},
        {"city": "Burnaby", "id": "60", "desc": "Cameron Recreation Complex"},
        {"city": "Burnaby", "id": "61", "desc": "Capitol Hill Elementary School"},
        {"city": "Burnaby", "id": "62", "desc": "Cariboo Hill Secondary School"},
        {"city": "Burnaby", "id": "63", "desc": "Cascade Heights Elementary School"},
        {"city": "Burnaby", "id": "64", "desc": "Central Park"},
        {"city": "Burnaby", "id": "65", "desc": "CG Brown Memorial Pool"},
        {"city": "Burnaby", "id": "66", "desc": "Chaffey-Burke Elementary School"},
        {"city": "Burnaby", "id": "67", "desc": "Clinton Elementary School"},
        {"city": "Burnaby", "id": "68", "desc": "Confederation Park"},
        {"city": "Burnaby", "id": "69", "desc": "Confederation Park Elementary School"},
        {"city": "Burnaby", "id": "70", "desc": "Deer Lake Park"},
        {"city": "Burnaby", "id": "71", "desc": "Douglas Road Elementary School"},
        {"city": "Burnaby", "id": "72", "desc": "Eagle Creek YMCA Child Care"},
        {"city": "Burnaby", "id": "73", "desc": "Eastburn Community Centre"},
        {"city": "Burnaby", "id": "74", "desc": "Edmonds Community Centre"},
        {"city": "Burnaby", "id": "75", "desc": "Edmonds Park"},
        {"city": "Burnaby", "id": "76", "desc": "Forest Grove Elementary School"},
        {"city": "Burnaby", "id": "77", "desc": "Gilmore Community School"},
        {"city": "Burnaby", "id": "78", "desc": "Gilpin Elementary School"},
        {"city": "Burnaby", "id": "79", "desc": "Glenwood Elementary School"},
        {"city": "Burnaby", "id": "80", "desc": "Hammerskjold Elementary School"},
        {"city": "Burnaby", "id": "81", "desc": "Hastings Community Centre"},
        {"city": "Burnaby", "id": "82", "desc": "Hastings Community School"},
        {"city": "Burnaby", "id": "83", "desc": "Inman Elementary School"},
        {"city": "Burnaby", "id": "84", "desc": "Kensington Park"},
        {"city": "Burnaby", "id": "85", "desc": "Kensington Park Arena"},
        {"city": "Burnaby", "id": "86", "desc": "Kitchener Elementary School"},
        {"city": "Burnaby", "id": "87", "desc": "Lakeview Elementary School"},
        {"city": "Burnaby", "id": "88", "desc": "Lochdale Community School"},
        {"city": "Burnaby", "id": "89", "desc": "Lyndhurst Elementary School"},
        {"city": "Burnaby", "id": "90", "desc": "Marlborough Elementary School"},
        {"city": "Burnaby", "id": "91", "desc": "Maywood Community School"},
        {"city": "Burnaby", "id": "92", "desc": "McPherson Park"},
        {"city": "Burnaby", "id": "93", "desc": "Monteciteo Elementary School"},
        {"city": "Burnaby", "id": "94", "desc": "Morley Elementary School"},
        {"city": "Burnaby", "id": "95", "desc": "Moscrop Secondary School"},
        {"city": "Burnaby", "id": "96", "desc": "Nelson Elementary School"},
        {"city": "Burnaby", "id": "97", "desc": "Parkcrest Elementary School"},
        {"city": "Burnaby", "id": "98", "desc": "Rene Memorial Park"},
        {"city": "Burnaby", "id": "99", "desc": "Riverway Sports Complex"},
        {"city": "Burnaby", "id": "100", "desc": "Rosedale Park"},
        {"city": "Burnaby", "id": "101", "desc": "Rosser Elementary School"},
        {"city": "Burnaby", "id": "102", "desc": "Seaforth Elementary School"},
        {"city": "Burnaby", "id": "103", "desc": "Second Street Community School"},
        {"city": "Burnaby", "id": "104", "desc": "South Slope Elementary School"},
        {"city": "Burnaby", "id": "105", "desc": "Sperling Elementary School"},
        {"city": "Burnaby", "id": "106", "desc": "Stoney Creek Community School"},
        {"city": "Burnaby", "id": "107", "desc": "Stride Avenue Community School"},
        {"city": "Burnaby", "id": "108", "desc": "Suncrest Elementary School"},
        {"city": "Burnaby", "id": "109", "desc": "Swangard Stadium"},
        {"city": "Burnaby", "id": "110", "desc": "Taylor Park Elementary School"},
        {"city": "Burnaby", "id": "111", "desc": "Twelfth Avenue Elementary School"},
        {"city": "Burnaby", "id": "112", "desc": "University Highlands Elementary School"},
        {"city": "Burnaby", "id": "113", "desc": "Wesburn Park"},
        {"city": "Burnaby", "id": "114", "desc": "Westburn Park"},
        {"city": "Burnaby", "id": "115", "desc": "Westlake Elementary School"},
        {"city": "Burnaby", "id": "116", "desc": "Westminster Centre"},
        {"city": "Burnaby", "id": "117", "desc": "Westminster Park"},
        {"city": "Burnaby", "id": "118", "desc": "Westridge Elementary School"},
        {"city": "Burnaby", "id": "119", "desc": "Willingdon Community Centre"},
        {"city": "Burnaby", "id": "120", "desc": "Willingdon Heights Park"}
    ]
    
    for location in bc_locations:
        if location in query:
            locations.append(location)
    
    # Check for specific centers in the query
    query_lower = query.lower()
    for center in centers_list:
        if center["desc"].lower() in query_lower:
            locations.append(center["desc"])
    
    # General location terms
    if any(term in query for term in ["nearby", "close", "local"]):
        locations.append("nearby")
    
    if any(term in query for term in ["downtown", "central", "city center"]):
        locations.append("downtown")
    
    return list(set(locations))

def _extract_exclusions(query: str) -> Dict[str, List[str]]:
    """Extract exclusion terms from the query."""
    exclusions = defaultdict(list)
    
    # Regex to find words following negative keywords
    exclusion_pattern = r'(?:not|except|exclude|no)\s+([a-zA-Z\s,]+)'
    
    excluded_phrases = re.findall(exclusion_pattern, query, re.IGNORECASE)
    
    # Keywords for different categories
    category_keywords = {
        "aquatics": ["swim", "swimming", "pool", "water", "aquatic"],
        "fitness": ["fitness", "gym", "workout", "exercise", "strength"], 
        "sports": ["sport", "soccer", "football", "basketball", "hockey", "tennis"],
        "arts": ["art", "arts", "painting", "drawing", "music", "dance", "drama"],
        "biking": ["bike", "biking", "cycling", "bicycle", "pedalheads"]
    }

    if excluded_phrases:
        # Flatten the list of phrases and split by commas or spaces
        all_excluded_words = []
        for phrase in excluded_phrases:
            all_excluded_words.extend(re.split(r'[\s,]+', phrase.strip()))

        # Check for category keywords in the excluded words
        for category, keywords in category_keywords.items():
            if any(keyword in all_excluded_words for keyword in keywords):
                exclusions["categories"].append(category)
                
    return dict(exclusions)

def _detect_relationships(query: str) -> Dict[str, Any]:
    """Detect complex relationship patterns in query."""
    relationships = {}
    
    # Back-to-back pattern - should find classes close in time (within 30 minutes)
    if any(term in query for term in ["back to back", "back-to-back", "consecutive", "one after another"]):
        relationships["type"] = "consecutive"
        relationships["pattern"] = "back_to_back"
        relationships["max_gap_minutes"] = 30  # Maximum gap between classes
        relationships["prefer_minimal_gaps"] = True
    
    # Same time / together pattern
    elif any(term in query for term in ["same time", "at the same time", "together", "simultaneous", "both at", "both together"]):
        relationships["type"] = "simultaneous"
        relationships["pattern"] = "overlap"
    
    # Same day pattern
    elif any(term in query for term in ["same day", "both on", "multiple on"]):
        relationships["type"] = "same_day"
        relationships["pattern"] = "multiple_same_day"
    
    # Specific scheduling patterns
    if "tuesday afternoon" in query and any(term in query for term in ["back", "consecutive", "multiple"]):
        relationships["day_specific"] = "tuesday"
        relationships["time_specific"] = "afternoon"
    
    # Duration constraints
    duration_pattern = r'(\d+)\s*(hour|hr|minute|min)'
    matches = re.findall(duration_pattern, query, re.IGNORECASE)
    if matches:
        durations = []
        for amount, unit in matches:
            durations.append(f"{amount} {unit}")
        relationships["duration_constraints"] = durations
    
    return relationships

def _get_llm_extracted_filters(query: str) -> Dict[str, Any]:
    """Use an LLM to extract detailed filters from the query."""
    try:
        model_name, _ = model_router.select_model_with_thinking(
            user_input=query,
            context={"task_type": TaskType.MEMORY_EXTRACTION}
        )
        model = genai.GenerativeModel(model_name)
        
        known_categories = [
            "Arts & Heritage", "Day Camps", "Fitness & Wellness", 
            "General Interest", "Gymnastics & Trampoline", "Sports",
            "Aquatics"
        ]
        
        prompt = f"""
        Analyze the user's query to extract structured search filters.
        User Query: "{query}"

        Extract the following:
        1. **Location**: Cities like "Burnaby" or "New Westminster". Return an empty list if none mentioned.
        2. **Activities**: List of activities with associated categories and ages. For each:
           - categories: From {', '.join(known_categories)}
           - age: Specific age mentioned (e.g., 5 for "5-year-old")
        3. **Relationships**: Type ("consecutive" or "simultaneous") and sequence of activities if applicable. For "back-to-back" or "consecutive" queries, always include at least two activities in the relationships.activities list, even if categories are empty.
        4. **Day of Week**: Specific days (e.g., "tuesday") or groups like "weekend", "weekdays".
        5. **Time of Day**: "morning", "afternoon", or "evening".
        6. **Price Constraints**: Max price (e.g., {{"max_price": 100}} for "under $100").
        7. **Special Requirements**: Qualitative needs like "competition preparation", "beginner level", or special needs like "autism", "wheelchair accessible".

        Return a JSON object with keys: "location", "activities", "relationships", "day_of_week", 
        "time_of_day", "price_constraints", "special_requirements".
        
        Examples:
        - Query: "Find back-to-back art classes on Tuesdays for my 5-year-old under $100"
          {{
              "location": [],
              "activities": [{{"categories": ["Arts & Heritage"], "age": 5}}],
              "relationships": {{"type": "consecutive", "activities": [{{"categories": ["Arts & Heritage"], "age": 5}}, {{"categories": ["Arts & Heritage"], "age": 5}}]}},
              "day_of_week": ["tuesday"],
              "time_of_day": [],
              "price_constraints": {{"max_price": 100}},
              "special_requirements": ""
          }}
        - Query: "Art class for my 5-year-old and soccer for my 8-year-old close together"
          {{
              "location": [],
              "activities": [
                  {{"categories": ["Arts & Heritage"], "age": 5}},
                  {{"categories": ["Sports"], "age": 8}}
              ],
              "relationships": {{"type": "consecutive", "activities": [{{"categories": ["Arts & Heritage"], "age": 5}}, {{"categories": ["Sports"], "age": 8}}]}},
              "day_of_week": [],
              "time_of_day": [],
              "price_constraints": {{}},
              "special_requirements": ""
          }}
        - Query: "Swimming for my 7-year-old with autism under $50"
          {{
              "location": [],
              "activities": [{{"categories": ["Aquatics"], "age": 7}}],
              "relationships": null,
              "day_of_week": [],
              "time_of_day": [],
              "price_constraints": {{"max_price": 50}},
              "special_requirements": "autism"
          }}
        - Query: "Are there classes that prepare my 10-year-old for gymnastics competitions?"
          {{
              "location": [],
              "activities": [{{"categories": ["Gymnastics & Trampoline"], "age": 10}}],
              "relationships": null,
              "day_of_week": [],
              "time_of_day": [],
              "price_constraints": {{}},
              "special_requirements": "competition preparation"
          }}
        """
        
        response = model.generate_content(prompt)
        match = re.search(r'\{.*\}', response.text, re.DOTALL)
        if match:
            return json.loads(match.group(0))
            
    except Exception as e:
        logger.error(f"LLM-based filter extraction failed: {e}")
    return {}
