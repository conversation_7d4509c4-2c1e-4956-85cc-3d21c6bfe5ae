#!/usr/bin/env python3

import asyncio
import json
import google.generativeai as genai
from qdrant_client import AsyncQdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance, PointIdsList, PayloadSchemaType
import sys
import os
from datetime import datetime
import time # Import time for time.sleep

# Add the multi_tool_agent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'multi_tool_agent'))

from config import AgentConfig
from multi_tool_agent.ingestion.data_utils import normalize_activity

def format_and_enrich_for_qdrant(activity: dict) -> dict:
    """
    Transforms and enriches scraper data into the format required for Qdrant.
    This includes creating a comprehensive text payload for embedding and ensuring
    consistent structured data for filtering.
    """
    # --- Data Consistency: Ensure fields for filtering exist ---
    # Standardize 'day_of_week' and 'days_of_week_list' for filtering
    if "days_of_week" in activity and "days_of_week_list" not in activity:
        days_of_week_raw = activity.get("days_of_week", "")
        days_of_week_list = []
        if days_of_week_raw:
            day_mapping = {
                "mon": "monday", "tue": "tuesday", "wed": "wednesday",
                "thu": "thursday", "fri": "friday", "sat": "saturday", "sun": "sunday"
            }
            # Handle formats like "Sunday" or "Every Mon, Tue"
            for day_abbr, full_name in day_mapping.items():
                if day_abbr in days_of_week_raw.lower() or full_name in days_of_week_raw.lower():
                    days_of_week_list.append(full_name)
            activity['days_of_week_list'] = sorted(list(set(days_of_week_list)))
            if activity['days_of_week_list']:
                activity['day_of_week'] = activity['days_of_week_list'][0]

    # Create comprehensive text for embedding from the original text fields
    text_parts = []
    if activity.get("name"): text_parts.append(activity['name'])
    if activity.get("description"): text_parts.append(activity['description'])
    if activity.get("category"): text_parts.append(activity['category'])
    if activity.get("age_info"): text_parts.append(activity.get('age_info'))
    if activity.get("location_general"): text_parts.append(activity.get('location_general'))
    if activity.get("start_time"): text_parts.append(activity.get('start_time'))

    # Use the normalized, flat activity as the primary payload
    normalized_activity = normalize_activity(activity)
    
    qdrant_record = {
        "id": activity["record_id"],
        "text_for_embedding": " | ".join(text_parts),
        "payload": normalized_activity,  # Use the normalized, flat structure
        "metadata": activity  # Keep original for backward compatibility
    }

    # Clean up null values from both payload and metadata
    qdrant_record['payload'] = {k: v for k, v in qdrant_record['payload'].items() if v is not None}
    qdrant_record['metadata'] = {k: v for k, v in qdrant_record['metadata'].items() if v is not None}
    
    return qdrant_record


async def ingest_activities_to_qdrant():
    """Ingest the newly scraped comprehensive activities into Qdrant."""
    
    print("=== Ingesting Comprehensive Activities to Qdrant ===")
    
    # Configure Google AI
    genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
    
    # Initialize Qdrant client
    qdrant_client = AsyncQdrantClient(
        url=AgentConfig.QDRANT_URL,
        api_key=AgentConfig.QDRANT_API_KEY
    )
    
    # Load activities from all available JSON source files
    activities = []
    # Use a dictionary to map files to a source name for clear data provenance
    source_map = {
        'multi_tool_agent/ingestion/fast_dropin_activities.json': 'New West PerfectMind',
        'multi_tool_agent/ingestion/burnaby_activities_complete.json': 'Burnaby ActiveCommunities'
    }

    for file_path, source_name in source_map.items():
        if os.path.exists(file_path):
            print(f"Loading activities from '{file_path}' (Source: {source_name})")
            with open(file_path, 'r', encoding='utf-8') as f:
                try:
                    loaded_activities = json.load(f)
                    # Add/overwrite the source for each activity for filtering
                    for activity in loaded_activities:
                        if 'source' not in activity:
                            activity['source'] = source_name
                    activities.extend(loaded_activities)
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON from {file_path}: {e}")
        else:
            print(f"Warning: Source file not found, skipping: {file_path}")

    # Fallback for old JSONL file
    jsonl_file = 'community_activities.jsonl'
    if os.path.exists(jsonl_file):
        print(f"Loading activities from legacy JSONL file: {jsonl_file}")
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line in f:
                activities.append(json.loads(line))
    if not activities:
        print(f"Error: No activities loaded. Check source files: {list(source_map.keys())} and {jsonl_file}")
        return False
    
    print(f"Loaded {len(activities)} comprehensive activities")
    source_ids = {activity['record_id'] for activity in activities}
    print(f"Found {len(source_ids)} unique IDs in the source file.")
    
    # Simplified collection check - rely on setup script for schema management
    try:
        await qdrant_client.get_collection(collection_name=AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Collection '{AgentConfig.QDRANT_COLLECTION_NAME}' exists. Syncing data.")
        collection_exists = True
    except Exception:
        print(f"CRITICAL ERROR: Collection '{AgentConfig.QDRANT_COLLECTION_NAME}' does not exist.")
        print("Please run the `multi_tool_agent/ingestion/setup_qdrant_collection.py` script first.")
        return False
    
    # If the collection exists, sync it by removing old entries
    if collection_exists:
        try:
            print("Fetching existing IDs from Qdrant to check for expired activities...")
            
            # Scroll through all points to get their IDs
            qdrant_ids = set()
            next_offset = None
            while True:
                records, next_offset = await qdrant_client.scroll(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    limit=250,
                    with_payload=False,
                    with_vectors=False,
                    offset=next_offset
                )
                if not records:
                    break
                
                qdrant_ids.update(point.id for point in records)
                
                if next_offset is None:
                    break
            
            print(f"Found {len(qdrant_ids)} existing IDs in Qdrant.")

            # Determine which IDs to delete
            ids_to_delete = list(qdrant_ids - source_ids)

            if ids_to_delete:
                print(f"Found {len(ids_to_delete)} activities to remove.")
                await qdrant_client.delete(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    points_selector=PointIdsList(points=ids_to_delete)
                )
                print("Successfully removed expired activities.")
            else:
                print("No expired activities to remove.")

        except Exception as e:
            print(f"Warning: Error during deletion of old activities: {e}")
            # Continue to upsert new data even if deletion fails
    
    # Process activities in batches
    batch_size = 100 # The embedding API can handle up to 100 texts per call
    
    print("Generating embeddings and creating points from comprehensive data...")
    
    # Prepare all records for embedding and then process in batches
    qdrant_records_to_process = [format_and_enrich_for_qdrant(activity) for activity in activities]
    
    for i in range(0, len(qdrant_records_to_process), batch_size):
        batch_records = qdrant_records_to_process[i:i + batch_size]
        texts_to_embed = [rec['text_for_embedding'] for rec in batch_records]

        try:
            print(f"Generating embeddings for batch {i//batch_size + 1}...")
            embedding_response = genai.embed_content(
                model="models/text-embedding-004",
                content=texts_to_embed,
                task_type="RETRIEVAL_DOCUMENT" # Specify the task for better embeddings
            )
            embeddings = embedding_response['embedding']

            points = []
            for record, embedding in zip(batch_records, embeddings):
                point = PointStruct(
                    id=record['id'],
                    vector=embedding,
                    payload=record['payload']  # Use the normalized, flat payload directly
                )
                points.append(point)

            print(f"Uploading batch of {len(points)} points...")
            await qdrant_client.upsert(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                points=points
            )
            time.sleep(1) # Sleep between batches to respect rate limits

        except Exception as e:
            print(f"Error processing batch starting at index {i}: {e}")
            continue # Skip to the next record
    
    # Verify collection
    collection_info = await qdrant_client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
    print(f"\nCollection updated successfully!")
    print(f"Total points in collection: {collection_info.points_count}")
    
    await qdrant_client.close()
    return True

if __name__ == "__main__":
    asyncio.run(ingest_activities_to_qdrant())
