#!/usr/bin/env python3
"""
Specialized tools for searching and processing activity data from Qdrant.
This module separates the core search logic from agent orchestration.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import collections
from datetime import datetime, timedelta

from .config import AgentConfig
from qdrant_client import AsyncQdrantClient, models
import google.generativeai as genai

# For type hinting
try:
    from google.genai.adk import ToolContext
except ImportError:
    ToolContext = object

logger = logging.getLogger(__name__)

# --- Helper Functions (Internal to this module) ---

def _create_qdrant_filter(
    age_constraints: Optional[Dict] = None, 
    category_filters: Optional[List[str]] = None,
    day_filters: Optional[List[str]] = None,
    category_exclusions: Optional[List[str]] = None
) -> Optional[models.Filter]:
    """Creates a Qdrant filter from a structured filter object."""
    must_conditions = []
    must_not_conditions = []
    
    # Age filter
    if age_constraints and age_constraints.get("min_age") is not None and age_constraints.get("max_age") is not None:
        must_conditions.append(
            models.FieldCondition(key="min_age_years", range=models.Range(lte=age_constraints["max_age"]))
        )
        must_conditions.append(
            models.FieldCondition(key="max_age_years", range=models.Range(gte=age_constraints["min_age"]))
        )
    
    # Category filter
    if category_filters:
        if len(category_filters) == 1:
            must_conditions.append(
                models.FieldCondition(key="category", match=models.MatchValue(value=category_filters[0]))
            )
        else:
            must_conditions.append(
                models.Filter(
                    should=[
                        models.FieldCondition(key="category", match=models.MatchValue(value=cat)) for cat in category_filters
                    ]
                )
            )

    # Day of week filter
    if day_filters:
        must_conditions.append(
            models.FieldCondition(key="days_of_week", match=models.MatchAny(any=day_filters))
        )

    # Category exclusion filter
    if category_exclusions:
        for cat in category_exclusions:
            must_not_conditions.append(
                models.FieldCondition(key="category", match=models.MatchValue(value=cat))
            )

    if not must_conditions and not must_not_conditions:
        return None
        
    return models.Filter(must=must_conditions if must_conditions else None, must_not=must_not_conditions if must_not_conditions else None)

def _find_back_to_back_pairs(classes: list, max_gap_minutes=30) -> list:
    """The core logic to find consecutive classes from a list."""
    grouped_classes = collections.defaultdict(list)
    for cls in classes:
        if not all(k in cls.get('metadata', {}) for k in ['general_location', 'days_of_week_list', 'start_time_iso', 'end_time_iso']):
            continue
        
        for day in cls['metadata']['days_of_week_list']:
            key = (cls['metadata']['general_location'], day)
            grouped_classes[key].append(cls['metadata'])

    back_to_back_pairs = []
    time_format = "%H:%M:%S"
    max_gap = timedelta(minutes=max_gap_minutes)

    for key, class_list in grouped_classes.items():
        sorted_classes = sorted(class_list, key=lambda c: c['start_time_iso'])
        
        for i in range(len(sorted_classes) - 1):
            class_1 = sorted_classes[i]
            class_2 = sorted_classes[i+1]
            
            try:
                end_time_1 = datetime.strptime(class_1['end_time_iso'], time_format)
                start_time_2 = datetime.strptime(class_2['start_time_iso'], time_format)

                if end_time_1 < start_time_2 and start_time_2 - end_time_1 <= max_gap:
                    back_to_back_pairs.append((class_1, class_2))
            except (ValueError, KeyError) as e:
                logger.warning(f"Skipping class pair due to data error: {e}")
                continue

    return back_to_back_pairs

# --- Public Tools for Agents ---

async def simple_vector_search(query: str, filters: Dict[str, Any], tool_context: ToolContext) -> Dict:
    """
    Performs a standard filtered vector search in Qdrant.
    If no category is specified, it diversifies the search across all categories.
    """
    logger.info(f"Performing simple vector search for: '{query}' with filters: {filters}")
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)

        embedding_response = genai.embed_content(
            model=AgentConfig.EMBEDDING_MODEL, content=query, task_type="RETRIEVAL_QUERY"
        )
        query_vector = embedding_response['embedding']

        # Diversify search if no category is specified and no exclusions
        if not filters.get("categories") and not filters.get("exclusions"):
            logger.info("No category specified, diversifying search.")
            all_categories = ["aquatics", "fitness", "sports", "arts", "biking", "general"]
            all_results = []
            seen_ids = set()

            for category in all_categories:
                qdrant_filter = _create_qdrant_filter(
                    age_constraints=filters.get("age_constraints"),
                    category_filters=[category],
                    day_filters=filters.get("days_of_week")
                )
                search_results = await qdrant_client.search(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    query_vector=query_vector,
                    query_filter=qdrant_filter,
                    limit=2,  # Get top 2 from each category
                    with_payload=True
                )
                for point in search_results:
                    if point.id not in seen_ids:
                        all_results.append(point.payload)
                        seen_ids.add(point.id)
            
            activities = all_results

        else:
            logger.info(f"Category specified, searching within: {filters.get('categories')}")
            qdrant_filter = _create_qdrant_filter(
                age_constraints=filters.get("age_constraints"),
                category_filters=filters.get("categories"),
                day_filters=filters.get("days_of_week"),
                category_exclusions=filters.get("exclusions", {}).get("categories")
            )
            search_results = await qdrant_client.search(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                query_vector=query_vector,
                query_filter=qdrant_filter,
                limit=10,  # Return a larger list for specific searches
                with_payload=True
            )
            activities = [point.payload for point in search_results]

        return {"status": "success", "results": activities}

    except Exception as e:
        logger.error(f"Error in simple_vector_search: {e}", exc_info=True)
        return {"status": "error", "message": str(e)}

async def find_consecutive_activities(query: str, filters: Dict[str, Any], tool_context: ToolContext) -> Dict:
    """
    Finds activities that are scheduled back-to-back based on location and day.
    Uses vector search to find relevant candidates first.
    """
    logger.info(f"Finding consecutive activities for '{query}' with filters: {filters}")
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)

        # Build a comprehensive filter including age, categories, days, and exclusions
        comprehensive_filter = _create_qdrant_filter(
            age_constraints=filters.get("age_constraints"),
            category_filters=filters.get("categories"),
            day_filters=filters.get("days_of_week"),
            category_exclusions=filters.get("exclusions", {}).get("categories")
        )
        
        # Embed the user's query to find semantically similar activities
        embedding_response = genai.embed_content(
            model=AgentConfig.EMBEDDING_MODEL, content=query, task_type="RETRIEVAL_QUERY"
        )
        
        # Use vector SEARCH (not scroll) to get a relevant set of candidates
        candidate_points = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=embedding_response['embedding'],
            query_filter=comprehensive_filter,
            limit=100,  # Get a good number of candidates to check for pairs
            with_payload=True
        )

        candidate_classes = [point.payload for point in candidate_points]
        logger.info(f"Found {len(candidate_classes)} candidates for back-to-back analysis.")

        back_to_back_pairs = _find_back_to_back_pairs(candidate_classes)
        logger.info(f"Found {len(back_to_back_pairs)} confirmed back-to-back pairs.")
        
        # Return a limited number of pairs to keep the context for the LLM small
        return {"status": "success", "results": back_to_back_pairs[:5]}

    except Exception as e:
        logger.error(f"Error in find_consecutive_activities: {e}", exc_info=True)
        return {"status": "error", "message": str(e)} 