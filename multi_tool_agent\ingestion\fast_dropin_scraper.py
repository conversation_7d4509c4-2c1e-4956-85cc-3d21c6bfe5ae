from __future__ import annotations

"""
Optimized and Fast Drop-in Activity Scraper for PerfectMind.

This scraper uses a systematic, filter-based approach to drastically reduce
redundant API calls and crawl time. It directly uses the filter IDs and payload
structure observed in browser network traffic for maximum reliability.

ENHANCED: Now properly fetches location names and pricing information.

Expected runtime: < 15 minutes.
"""

import asyncio
import json
import logging
import math
import re
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional

import httpx
from bs4 import BeautifulSoup

# --- Configuration ---
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

BASE_URL = "https://cityofnewwestminster.perfectmind.com"
ORG_ID = "23693"
WIDGET_ID = "50a33660-b4f7-44d9-9256-e10effec8641"
NAMESPACE_UUID = uuid.UUID("9e71ea85-7976-4395-a78b-1616c689eea7")

HTTP_TIMEOUT = 30.0
PAGE_SIZE = 100
CONCURRENT_REQUESTS = 5
MAX_RETRIES = 3
RETRY_DELAY = 2

_cur_dir = Path(__file__).parent


# --- Data Parsing & Expansion Functions ---
def _parse_age_range(age_str: Optional[str]) -> Dict[str, Optional[int]]:
    if not age_str: return {"min_age_years": None, "max_age_years": None}
    age_str = age_str.lower()
    patterns = [
        (re.compile(r"(\d+)\s+and\s+under"), lambda m: {"min_age_years": 0, "max_age_years": int(m.group(1))}),
        (re.compile(r"(\d+)\s*-\s*(\d+)"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": int(m.group(2))}),
        (re.compile(r"(\d+)\+"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": 99}),
        (re.compile(r"(\d+)\s+years\s+and\s+over"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": 99}),
    ]
    for pat, fn in patterns:
        m = pat.search(age_str)
        if m: return fn(m)
    return {"min_age_years": None, "max_age_years": None}

def _parse_time_range(time_str: Optional[str]) -> Dict[str, Any]:
    if not time_str: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    parts = [p.strip() for p in re.split(r"to|-", time_str, flags=re.IGNORECASE) if p.strip()]
    if len(parts) < 2: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    fmt_in, fmt_out = "%I:%M %p", "%H:%M:%S"
    try:
        start_dt, end_dt = datetime.strptime(parts[0], fmt_in), datetime.strptime(parts[1], fmt_in)
    except ValueError: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    dur = (end_dt - start_dt).total_seconds() / 60
    if dur < 0: dur += 24 * 60
    return {"start_time_iso": start_dt.strftime(fmt_out), "end_time_iso": end_dt.strftime(fmt_out), "duration_minutes": int(dur)}

def _parse_price(p: Optional[str]) -> Optional[float]:
    if not p: return None
    m = re.search(r"[\d\.]+", p)
    return float(m.group(0)) if m else None

def _expand_recurring_activity(activity: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Expand a recurring activity into individual occurrences based on date range and weekday pattern."""
    # Use the date range from EventDate field (already parsed into start_date and end_date)
    start_date_str = activity.get("start_date")
    end_date_str = activity.get("end_date")

    if not start_date_str or not end_date_str:
        return [activity]

    # Parse the date range
    date_fmt = "%d-%b-%Y"
    try:
        start_date = datetime.strptime(start_date_str, date_fmt)
        end_date = datetime.strptime(end_date_str, date_fmt)
    except (ValueError, TypeError):
        return [activity]

    # If it's a single day or invalid range, return as-is
    if start_date >= end_date:
        activity["day_of_week"] = start_date.strftime("%a").lower()
        return [activity]

    # Determine which days of the week this activity runs
    days_to_check = set()
    api_weekday_masks = activity.get("weekdays_mask")
    if isinstance(api_weekday_masks, list):
        # PerfectMind weekday mapping: {1: Sunday, 2: Monday, 4: Tuesday, 8: Wednesday, 16: Thursday, 32: Friday, 64: Saturday}
        mask_to_weekday = {1: 6, 2: 0, 4: 1, 8: 2, 16: 3, 32: 4, 64: 5}
        for mask_val in api_weekday_masks:
            if mask_val in mask_to_weekday:
                days_to_check.add(mask_to_weekday[mask_val])

    # Fallback: parse from days_of_week text
    if not days_to_check:
        days_of_week_raw = activity.get("days_of_week") or ""
        weekday_map = {"mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6}
        day_tokens = {t[:3] for t in re.findall(r"[A-Za-z]+", days_of_week_raw.lower())}
        days_to_check.update(weekday_map[t] for t in day_tokens if t in weekday_map)

    # If no specific days, assume it runs every day
    run_every_day = not days_to_check

    # Generate individual occurrences
    occurrences = []
    current = start_date
    delta = timedelta(days=1)
    base_uuid = activity.get("record_id") or str(uuid.uuid4())

    while current <= end_date:
        if run_every_day or current.weekday() in days_to_check:
            occ = activity.copy()
            occ_date_str = current.strftime(date_fmt)

            # Each occurrence is a single day
            occ["start_date"] = occ_date_str
            occ["end_date"] = occ_date_str
            occ["day_of_week"] = current.strftime("%a").lower()

            # Generate unique record ID for this occurrence
            occ["record_id"] = str(uuid.uuid5(NAMESPACE_UUID, f"{base_uuid}-{occ_date_str}"))
            occurrences.append(occ)

        current += delta

    return occurrences if occurrences else [activity]



def _parse_event_date(date_str: Optional[str]) -> Dict[str, Optional[str]]:
    """Parse EventDate field which can be a single date or range like '14-Jul-2025 - 17-Jul-2025'"""
    if not date_str:
        return {"start_date": None, "end_date": None}
    if ' - ' in date_str:
        parts = [p.strip() for p in date_str.split(' - ')]
        if len(parts) == 2:
            return {"start_date": parts[0], "end_date": parts[1]}
    return {"start_date": date_str, "end_date": date_str}

def _parse_timestamp_date(timestamp_str: Optional[str]) -> Optional[str]:
    """Parse /Date(timestamp)/ format from PerfectMind API"""
    if not timestamp_str or not timestamp_str.startswith('/Date('):
        return None
    try:
        timestamp = int(timestamp_str.replace('/Date(', '').replace(')/', ''))
        dt = datetime.fromtimestamp(timestamp / 1000)
        return dt.strftime('%d-%b-%Y')
    except (ValueError, TypeError):
        return None



def _summarize(act: Dict[str, Any]) -> Dict[str, Any]:
    orig_id = act.get("RecordId")
    uniq_key = f"{orig_id}-{act.get('EventDate')}-{act.get('EventTime')}-{act.get('EventDays')}"
    
    # Check all possible location-related fields
    location_fields = {
        'LocationId': act.get('LocationId'),
        'LocationID': act.get('LocationID'),
        'Location': act.get('Location'),
        'LocationName': act.get('LocationName'),
        'FacilityId': act.get('FacilityId'),
        'FacilityID': act.get('FacilityID'),
        'Facility': act.get('Facility'),
        'FacilityName': act.get('FacilityName'),
    }
    
    # Log first activity to see all fields
    if not hasattr(_summarize, '_first_log'):
        _summarize._first_log = True
        logger.info(f"First activity full data: {json.dumps(act, indent=2)}")
        logger.info(f"Location-related fields found: {location_fields}")
    
    # Parse program date range from EventDate field (contains the full program range)
    # EventDate format: "07-Jul-2025 - 10-Jul-2025" or "07-Jul-2025" for single day
    start_date = None
    end_date = None

    # 1. Parse EventDate field which contains the program date range
    date_info = _parse_event_date(act.get("EventDate"))
    start_date = date_info["start_date"]
    end_date = date_info["end_date"]

    # 2. Fallback: Use EventCurrentStartInJsFormat for single occurrence
    if not start_date and act.get("EventCurrentStartInJsFormat"):
        try:
            dt = datetime.strptime(act.get("EventCurrentStartInJsFormat"), "%Y-%m-%d")
            occurrence_date = dt.strftime('%d-%b-%Y')
            start_date = occurrence_date
            end_date = occurrence_date
        except (ValueError, TypeError):
            pass

    # 3. Last resort: Try timestamp fields
    if not start_date and act.get("EventStartUtc"):
        start_date = _parse_timestamp_date(act.get("EventStartUtc"))
    if not end_date and act.get("EventEnd"):
        end_date = _parse_timestamp_date(act.get("EventEnd"))

    # 4. Ensure we have an end_date
    if start_date and not end_date:
        end_date = start_date
    
    out = {
        "record_id": str(uuid.uuid5(NAMESPACE_UUID, uniq_key)),
        "name": act.get("EventTitle"),
        "activity_url": f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/CoursesLandingPage?widgetId={WIDGET_ID}&courseId={orig_id}",
        "description": act.get("EventDescription"),
        "category": act.get("CalendarCategory"),
        "general_location": act.get("FacilityName"),
        "location": act.get("LocationName"),  # This will be null initially
        "location_id": act.get("LocationId") or act.get("LocationID") or act.get("FacilityId") or act.get("FacilityID"),  # Try multiple fields
        "age_info": act.get("Age"),
        "start_date": start_date,
        "end_date": end_date,
        "start_time": act.get("EventTime"),
        "end_time": act.get("EventTimeEnd"),
        "days_of_week": act.get("EventDays"),
        "price": act.get("Price"),  # This will likely be null
        "fees": act.get("Fees"),  # This will likely be null too
        "is_full": act.get("IsFull", False),
        "registration_status": "open" if not act.get("IsRegistrationClosed") else "closed",
        "course_guid": orig_id,
        "weekdays_mask": act.get("Weekdays"),
        # Preserve original API fields for date parsing
        "EventCurrentStartInJsFormat": act.get("EventCurrentStartInJsFormat"),
        "EventStartUtc": act.get("EventStartUtc"),
        "EventEnd": act.get("EventEnd"),
        "EventDate": act.get("EventDate")
    }
    out.update(_parse_age_range(out.get("age_info")))
    out.update(_parse_time_range(out.get("start_time")))
    out["price_numeric"] = _parse_price(out.get("price"))
    return out


# --- Main Scraper Class ---
class FastDropInScraper:
    API_URL = f"{BASE_URL}/{ORG_ID}/Clients/Planner2/SearchActivitiesForBookMe"
    SEARCH_URL = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4Search"
    WIDGET_URL = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4?widgetId={WIDGET_ID}"

    def __init__(self) -> None:
        self.http_client = httpx.AsyncClient(timeout=HTTP_TIMEOUT, follow_redirects=True, http2=True)
        self.semaphore = asyncio.Semaphore(CONCURRENT_REQUESTS)
        self._csrf: Optional[str] = None
        # ENHANCEMENT: Add storage for all major filter types
        self.widget_locations: List[str] = []
        self.widget_calendars: List[Dict] = []
        self.filter_categories: List[Dict] = []
        self.filter_seasons: List[Dict] = []
        self.filter_services: List[Dict] = []
        # NEW: Location mapping
        self.location_mapping: Dict[str, str] = {}

    async def aclose(self):
        await self.http_client.aclose()

    async def _get_csrf(self) -> Optional[str]:
        if self._csrf: return self._csrf
        try:
            async with self.semaphore:
                r = await self.http_client.get(self.WIDGET_URL)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, 'html.parser')
            token_input = soup.find('input', {'name': '__RequestVerificationToken'})
            if token_input and 'value' in token_input.attrs:
                self._csrf = token_input['value']
                logger.info("Successfully acquired CSRF token.")
                return self._csrf
            logger.error("Could not find __RequestVerificationToken on widget page.")
        except httpx.RequestError as e:
            logger.error(f"CSRF token request error from {self.WIDGET_URL}: {e}")
        return None

    async def _make_request_with_retry(self, method: str, url: str, **kwargs) -> Optional[httpx.Response]:
        retries = 0
        while retries <= MAX_RETRIES:
            try:
                async with self.semaphore:
                    headers = kwargs.pop("headers", {})
                    headers.setdefault("X-Requested-With", "XMLHttpRequest")
                    
                    if method.lower() == "get":
                        response = await self.http_client.get(url, headers=headers, **kwargs)
                    else:
                        response = await self.http_client.post(url, headers=headers, **kwargs)

                if response.status_code < 400:
                    return response
                if 400 <= response.status_code < 500 and response.status_code != 429:
                    logger.warning(f"Client error {response.status_code} for {url}. Response: {response.text[:200]}")
                    return None
                logger.warning(f"Request failed with status {response.status_code}, retrying ({retries+1}/{MAX_RETRIES})...")
            except httpx.RequestError as e:
                logger.warning(f"Request error: {e}, retrying ({retries+1}/{MAX_RETRIES})...")
            await asyncio.sleep(RETRY_DELAY * (2 ** retries))
            retries += 1
        logger.error(f"Failed to make request to {url} after {MAX_RETRIES} retries")
        return None

    async def _bootstrap_filters(self) -> bool:
        logger.info("--- Bootstrapping ALL filter IDs from search page ---")
        if not self._csrf:
            logger.error("Cannot bootstrap filters without CSRF token.")
            return False
            
        payload = { "__RequestVerificationToken": self._csrf, "WidgetId": WIDGET_ID, "searchText": "", "searchDropIn": "true", "searchCourses": "true" }
        response = await self._make_request_with_retry("post", self.SEARCH_URL, data=payload)
        if not response:
            logger.error("Failed to POST to search page for bootstrapping filters.")
            return False

        soup = BeautifulSoup(response.text, 'html.parser')
        script_tag = soup.find('script', string=re.compile(r'var locations = JSON.parse'))
        if not script_tag or not script_tag.string:
            logger.error("Could not find filter data script on search page. The page might have changed or we were redirected.")
            return False

        script_content = script_tag.string
        try:
            # ENHANCEMENT: Parse all available filter types from the page's JS
            filter_map = {
                'locations': ('widget_locations', r"var locations = JSON.parse\('(.+?)'\);"),
                'calendars': ('widget_calendars', r"var calendars = JSON.parse\('(.+?)'\);"),
                'categories': ('filter_categories', r"var categories = JSON.parse\('(.+?)'\);"),
                'seasons': ('filter_seasons', r"var seasons = JSON.parse\('(.+?)'\);"),
                'services': ('filter_services', r"var services = JSON.parse\('(.+?)'\);"),
            }
            
            for name, (attr, pattern) in filter_map.items():
                match = re.search(pattern, script_content)
                if match:
                    data = json.loads(match.group(1))
                    setattr(self, attr, data)
                    logger.info(f"Discovered {len(data)} {name}.")

            # FIXED: Build location mapping from the locations data
            locations_data = getattr(self, 'widget_locations', [])
            if isinstance(locations_data, list):
                for location in locations_data:
                    if isinstance(location, dict) and 'Value' in location and 'Text' in location:
                        # Only map non-empty location IDs
                        if location['Value'].strip():
                            self.location_mapping[location['Value']] = location['Text']
                logger.info(f"Built location mapping with {len(self.location_mapping)} entries.")
                logger.debug(f"Location mapping sample: {dict(list(self.location_mapping.items())[:5])}")
            
            # Add manual mapping for common facilities to their parent locations
            self.facility_to_location_mapping = {
                "Gymnasium (large)": "təməsew̓txʷ Aquatic and Community Centre",
                "Gymnasium (small)": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure Pool - South": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure Pool - North": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure & Lap Pool": "təməsew̓txʷ Aquatic and Community Centre",
                "Lap Pool": "təməsew̓txʷ Aquatic and Community Centre",
                "Fitness Centre": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 1": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 2": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 3": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 4": "təməsew̓txʷ Aquatic and Community Centre",
                "Multipurpose 1A": "təməsew̓txʷ Aquatic and Community Centre",
                "Multipurpose 1B": "təməsew̓txʷ Aquatic and Community Centre",
                "Anvil Theatre": "Anvil Centre",
                "Conference Room": "Anvil Centre",
                "Gymnasium": "Centennial Community Centre",
                "Fitness Studio": "Centennial Community Centre",
                "Pool": "Centennial Community Centre",
                "Rink 1": "Moody Park Arena",
                "Rink 2": "Moody Park Arena",
                "Rink": "Queensborough Community Centre",
                "Meeting Room": "Queensborough Community Centre",
            }
            
            # Add manual mapping for unmapped location IDs we discovered
            self.location_id_override_mapping = {
                "771caa56-e1fd-4177-a689-4fae4662e96f": "təməsew̓txʷ Aquatic and Community Centre",  # Leisure & Lap Pool
                "681dc09c-0eb8-4179-affa-233e2b24227c": "təməsew̓txʷ Aquatic and Community Centre",  # Multipurpose rooms
            }

        except (json.JSONDecodeError, IndexError) as e:
            logger.error(f"Failed to parse filter JSON from search page: {e}")
            return False

        return bool(self.widget_locations and self.widget_calendars)

    async def _fetch_with_payload(self, payload: Dict[str, Any], search_desc: str) -> List[Dict[str, Any]]:
        all_activities = []
        headers = {"RequestVerificationToken": self._csrf}
        try:
            response = await self._make_request_with_retry("post", self.API_URL, json={**payload, "page": 1}, headers=headers)
            if not response: return []
            data = response.json()
            initial_activities = data.get("Activities", [])
            total_items = data.get("Total", 0)
        except Exception as e:
            logger.warning(f"Initial request for '{search_desc}' failed: {e}")
            return []

        if not initial_activities: return []
        
        all_activities.extend(initial_activities)
        num_pages = math.ceil(total_items / PAGE_SIZE)
        
        if num_pages > 1:
            num_pages = min(num_pages, 20)
            for page_num in range(2, num_pages + 1):
                page_payload = {**payload, "page": page_num}
                try:
                    await asyncio.sleep(0.25)
                    response = await self._make_request_with_retry("post", self.API_URL, json=page_payload, headers=headers)
                    if response: all_activities.extend(response.json().get("Activities", []))
                except Exception: continue
        
        logger.info(f"  -> Found {len(all_activities):>4} raw activities for '{search_desc}'")
        return all_activities

    async def _fetch_and_parse_details(self, activity: Dict[str, Any]) -> Dict[str, Any]:
        class_id = activity.get("course_guid")
        if not class_id: return {}
        
        # First try the CoursesLandingPage URL which has pricing info
        course_url = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/CoursesLandingPage?widgetId={WIDGET_ID}&courseId={class_id}"
        response = await self._make_request_with_retry("get", course_url)
        
        # If that fails, try the Class page as fallback
        if not response:
            start_date_str = activity.get("start_date")
            if start_date_str:
                try:
                    date_to_parse = start_date_str.split(' - ')[0].strip()
                    occurrence_date = datetime.strptime(date_to_parse, "%d-%b-%Y").strftime("%Y%m%d")
                    details_url = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/Class?widgetId={WIDGET_ID}&classId={class_id}&occurrenceDate={occurrence_date}"
                    response = await self._make_request_with_retry("get", details_url)
                except (ValueError, TypeError):
                    pass
        
        if not response:
            logger.debug(f"Failed to fetch details for {class_id}")
            return {}
        
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            details = {}
            
            # Strategy 1: Look for price in bm-price-tag divs
            price_elements = soup.find_all('div', class_='bm-price-tag')
            if price_elements:
                for price_element in price_elements:
                    price_text = price_element.get_text(strip=True)
                    price_match = re.search(r'\$([\d,]+\.?\d*)', price_text)
                    if price_match:
                        try:
                            price_value = float(price_match.group(1).replace(',', ''))
                            details["price_numeric"] = price_value
                            details["price"] = f"${price_value:.2f}"
                            details["fees"] = f"${price_value:.2f}"
                            break
                        except ValueError:
                            continue
            
            # Strategy 2: Look for price in table cells with "Fee" or "Price" labels
            if not details.get("price_numeric"):
                # Look for table rows containing fee/price information
                for row in soup.find_all('tr'):
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True).lower()
                        if any(keyword in label for keyword in ['fee', 'price', 'cost', 'rate']):
                            value_text = cells[1].get_text(strip=True)
                            price_match = re.search(r'\$([\d,]+\.?\d*)', value_text)
                            if price_match:
                                try:
                                    price_value = float(price_match.group(1).replace(',', ''))
                                    details["price_numeric"] = price_value
                                    details["price"] = f"${price_value:.2f}"
                                    details["fees"] = f"${price_value:.2f}"
                                    break
                                except ValueError:
                                    continue
            
            # Strategy 3: Look for price in any element with class containing 'price' or 'fee'
            if not details.get("price_numeric"):
                price_elements = soup.find_all(class_=re.compile(r'(price|fee|cost)', re.I))
                for elem in price_elements:
                    elem_text = elem.get_text(strip=True)
                    price_match = re.search(r'\$([\d,]+\.?\d*)', elem_text)
                    if price_match:
                        try:
                            price_value = float(price_match.group(1).replace(',', ''))
                            if 0.5 <= price_value <= 10000:  # Reasonable price range
                                details["price_numeric"] = price_value
                                details["price"] = f"${price_value:.2f}"
                                details["fees"] = f"${price_value:.2f}"
                                break
                        except ValueError:
                            continue
            
            # Strategy 4: Look for price patterns in the full text
            if not details.get("price_numeric"):
                all_text = soup.get_text()
                # Look for patterns like "Fee: $67.50" or "Price $67.50"
                price_patterns = [
                    r'(?:fee|price|cost|rate)[:\s]*\$?([\d,]+\.?\d*)',
                    r'\$?([\d,]+\.?\d*)\s*(?:fee|price|cost|rate)',
                    r'\$([\d,]+\.?\d*)'
                ]
                
                for pattern in price_patterns:
                    price_matches = re.findall(pattern, all_text, re.IGNORECASE)
                    if price_matches:
                        for match in price_matches:
                            try:
                                price_value = float(match.replace(',', '').replace('$', ''))
                                if 0.5 <= price_value <= 10000:  # Reasonable price range
                                    details["price_numeric"] = price_value
                                    details["price"] = f"${price_value:.2f}"
                                    details["fees"] = f"${price_value:.2f}"
                                    break
                            except ValueError:
                                continue
                        if details.get("price_numeric"):
                            break
            
            # Log if no price found
            if not details.get("price_numeric"):
                logger.debug(f"No price found for activity: {activity.get('name')} ({class_id}) - URL: {course_url}")
                # Save a snippet of the page for debugging
                text_snippet = soup.get_text()[:500].replace('\n', ' ').replace('\r', ' ')
                logger.debug(f"Page snippet: {text_snippet}")

            # Note: Date ranges are now extracted directly from the API EventDate field
            # No need to scrape date ranges from course detail pages
            # Try to extract location from eventInfo JSON (fallback)
            script_tag = soup.find('script', string=re.compile(r'\bvar\s+eventInfo\s*='))
            if script_tag and script_tag.string:
                script_content = script_tag.string
                assignment_pos = script_content.find('var eventInfo =')
                if assignment_pos != -1:
                    json_start_pos = script_content.find('{', assignment_pos)
                    if json_start_pos != -1:
                        json_candidate_str = script_content[json_start_pos:]
                        open_braces, json_end_pos = 0, -1
                        for i, char in enumerate(json_candidate_str):
                            if char == '{': open_braces += 1
                            elif char == '}': open_braces -= 1
                            if open_braces == 0: json_end_pos = i; break
                        if json_end_pos != -1:
                            try:
                                event_info = json.loads(json_candidate_str[:json_end_pos + 1])
                                specific_location = event_info.get("ActualLocation")
                                if specific_location:
                                    details["location"] = specific_location
                            except json.JSONDecodeError:
                                pass
            
            return details
            
        except Exception as e:
            logger.debug(f"Error parsing details for {class_id}: {e}")
            return {}

    def _apply_location_mapping(self, activities: List[Dict[str, Any]]) -> None:
        """Apply location ID to name mapping for all activities."""
        mapped_count = 0
        missing_location_id_count = 0
        unmapped_location_ids = set()
        facility_mapped_count = 0
        override_mapped_count = 0
        
        # Define override mappings for known unmapped location IDs
        location_id_overrides = {
            "771caa56-e1fd-4177-a689-4fae4662e96f": "təməsew̓txʷ Aquatic and Community Centre",  # Leisure & Lap Pool
            "681dc09c-0eb8-4179-affa-233e2b24227c": "təməsew̓txʷ Aquatic and Community Centre",  # Multipurpose rooms
            "985f3082-91da-4214-9ca3-de1d578f7520": "təməsew̓txʷ Aquatic and Community Centre",
            "85b00f8d-4eb6-4bc0-922a-1a8ee5db4d74": "təməsew̓txʷ Aquatic and Community Centre",
            "56cbf58d-30ed-4dac-afaa-9d6f42453a19": "təməsew̓txʷ Aquatic and Community Centre",
            "471e12ef-3676-4905-a150-ad370753fad7": "təməsew̓txʷ Aquatic and Community Centre",  # Gymnasium (large)
        }
        
        for activity in activities:
            # First try location ID mapping
            location_id = activity.get("location_id")
            if location_id and location_id in self.location_mapping:
                if not activity.get("location"):  # Only set if not already set from details
                    activity["location"] = self.location_mapping[location_id]
                    mapped_count += 1
            elif location_id and location_id in location_id_overrides:
                if not activity.get("location"):
                    activity["location"] = location_id_overrides[location_id]
                    override_mapped_count += 1
            elif location_id:
                unmapped_location_ids.add(location_id)
            else:
                missing_location_id_count += 1
            
            # If no location yet, try facility name mapping
            if not activity.get("location") and activity.get("general_location"):
                facility_name = activity["general_location"]
                if facility_name in self.facility_to_location_mapping:
                    activity["location"] = self.facility_to_location_mapping[facility_name]
                    facility_mapped_count += 1
        
        logger.info(f"Applied location ID mapping to {mapped_count} activities.")
        logger.info(f"Applied override location ID mapping to {override_mapped_count} activities.")
        logger.info(f"Applied facility name mapping to {facility_mapped_count} activities.")
        logger.info(f"Activities without location_id: {missing_location_id_count}")
        if unmapped_location_ids:
            logger.warning(f"Found {len(unmapped_location_ids)} unmapped location IDs: {list(unmapped_location_ids)[:5]}...")

    async def scrape(self) -> List[Dict[str, Any]]:
        token = await self._get_csrf()
        if not token or not await self._bootstrap_filters():
            logger.error("Failed to get CSRF token or bootstrap filters. Aborting.")
            return []
        
        base_payload = { "widgetId": WIDGET_ID, "searchDropIn": True, "searchCourses": True, "widgetLocations": self.widget_locations, "widgetCalendars": self.widget_calendars, "TimeFilter": 1 }
        
        all_raw_activities: List[Dict[str, Any]] = []
        search_tasks = []
        
        # --- ENHANCEMENT: Multi-pronged search strategy ---
        
        # Strategy 1: Keyword Search (Broad net)
        logger.info("\n--- Strategy 1: Crawling by common keywords ---")
        search_terms = ["", "drop-in", "gym", "fitness", "skate", "swim", "yoga", "sport", "art", "dance"]
        for term in search_terms:
            payload = {**base_payload, "searchText": term}
            search_tasks.append(self._fetch_with_payload(payload, f"Keyword: {term or 'ALL'}"))
        
        # Strategy 2: Iterate through every specific Category
        logger.info("\n--- Strategy 2: Crawling by every Category filter ---")
        for category in self.filter_categories:
            cat_id = category.get("Value")
            if cat_id and cat_id != "-1": # Skip "All Categories"
                payload = {**base_payload, "Category": cat_id}
                search_tasks.append(self._fetch_with_payload(payload, f"Category: {category.get('Text')}"))

        # Strategy 3: Iterate through every specific Season
        logger.info("\n--- Strategy 3: Crawling by every Season filter ---")
        for season in self.filter_seasons:
            season_id = season.get("Value")
            if season_id and season_id != "-1": # Skip "All Seasons"
                payload = {**base_payload, "Season": season_id}
                search_tasks.append(self._fetch_with_payload(payload, f"Season: {season.get('Text')}"))

        # Strategy 4: Iterate through every specific Service
        logger.info("\n--- Strategy 4: Crawling by every Service filter ---")
        for service in self.filter_services:
            service_id = service.get("Value")
            if service_id and service_id != "-1": # Skip "All Services"
                payload = {**base_payload, "ServiceId": service_id}
                search_tasks.append(self._fetch_with_payload(payload, f"Service: {service.get('Text')}"))

        results = await asyncio.gather(*search_tasks)
        for res in results: all_raw_activities.extend(res)
        
        logger.info("\n--- Finalizing: De-duplicating and processing all results ---")
        unique_raw_activities: Dict[str, Dict[str, Any]] = {
            f"{act.get('RecordId')}-{act.get('EventDate')}-{act.get('EventTime')}-{act.get('EventDays')}": act
            for act in all_raw_activities if act.get("RecordId")
        }
        logger.info(f"Found {len(unique_raw_activities)} unique raw activities across all strategies.")
        
        summaries = [_summarize(act) for act in unique_raw_activities.values()]
        
        # Apply location mapping before details fetching
        self._apply_location_mapping(summaries)
        
        unique_courses = list({s["course_guid"]: s for s in summaries if s.get("course_guid")}.values())
        
        logger.info(f"\n--- Fetching details for {len(unique_courses)} unique courses ---")
        
        # Process details in batches to avoid overwhelming the server
        batch_size = 50
        details_map = {}
        
        for i in range(0, len(unique_courses), batch_size):
            batch = unique_courses[i:i+batch_size]
            logger.info(f"Processing details batch {i//batch_size + 1}/{(len(unique_courses) + batch_size - 1)//batch_size}")
            
            details_tasks = [self._fetch_and_parse_details(course) for course in batch]
            details_results = await asyncio.gather(*details_tasks)
            
            for course, result in zip(batch, details_results):
                details_map[course["course_guid"]] = result
            
            # Add a small delay between batches
            if i + batch_size < len(unique_courses):
                await asyncio.sleep(1)
        
        # Apply details to all summaries
        for summary in summaries:
            if course_guid := summary.get("course_guid"):
                course_details = details_map.get(course_guid, {})
                summary.update(course_details)
        
        # Expand recurring activities into individual occurrences
        final_occurrences = []
        for summary in summaries:
            # Expand each activity based on its date range and weekday pattern
            final_occurrences.extend(_expand_recurring_activity(summary))
        
        # Apply location mapping again to expanded occurrences
        self._apply_location_mapping(final_occurrences)
        
        # Keep location_id in the output for debugging
        # Don't remove it: occurrence.pop("location_id", None)
        
        logger.info(f"After recurrence expansion and details fetching: {len(final_occurrences)} total occurrences.")
        return final_occurrences

def normalize_text(text: str) -> str:
    if not text: return ""
    text = text.lower()
    text = re.sub(r'\s+', ' ', text)
    text = text.replace('-', ' ').replace('!', '').replace('.', '').replace(',', '')
    return text.strip()

async def main():
    start_time = time.time()
    scraper = FastDropInScraper()
    try:
        activities = await scraper.scrape()
        if not activities:
            logger.warning("Crawl complete. No activities found.")
            return
        
        logger.info("\n--- Final Verification ---")
        targets = ["Gymnastics Family - Drop-in", "Gymnastics - Motoring Munchkins", "Try-it! Gymtime"]
        for target in targets:
            found_count = sum(1 for act in activities if normalize_text(target) in normalize_text(act.get("name", "")))
            if found_count > 0:
                logger.info(f"✅ SUCCESS: Found {found_count} occurrences of '{target}' or similar.")
            else:
                logger.warning(f"❌ WARNING: Target '{target}' was NOT found in final results.")
        
        # Log pricing and location stats
        activities_with_price = sum(1 for act in activities if act.get("price_numeric") is not None)
        activities_with_location = sum(1 for act in activities if act.get("location") is not None)
        
        logger.info(f"📊 STATS: {activities_with_price}/{len(activities)} activities have pricing info")
        logger.info(f"📊 STATS: {activities_with_location}/{len(activities)} activities have location info")
        
        out_file = _cur_dir / "fast_dropin_activities.json"
        with open(out_file, "w", encoding="utf-8") as fh:
            json.dump(activities, fh, indent=2, ensure_ascii=False)
        logger.info(f"\n✅ Saved {len(activities)} total activity occurrences to {out_file}")
        elapsed_time = time.time() - start_time
        logger.info(f"Total execution time: {elapsed_time:.2f} seconds ({elapsed_time/60:.2f} minutes)")
    finally:
        await scraper.aclose()


if __name__ == "__main__":
    asyncio.run(main())
