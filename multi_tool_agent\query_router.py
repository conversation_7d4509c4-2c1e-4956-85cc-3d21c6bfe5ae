#!/usr/bin/env python3
"""
Intelligent Query Router for Activity Search System

This module determines whether a query should be routed to:
1. Local activity database (Qdrant)
2. Web search
3. Memory system
4. Other tools

As the system scales to multiple cities and data sources, this router
ensures queries are handled by the most appropriate data source.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Literal
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of queries the system can handle."""
    ACTIVITY_SEARCH = "activity_search"      # Local database search
    WEB_SEARCH = "web_search"               # General web search
    MEMORY_QUERY = "memory_query"           # Personal memory retrieval
    TOOL_USAGE = "tool_usage"               # Time, weather, etc.
    CONVERSATION = "conversation"           # General chat

class DataSource(Enum):
    """Available data sources."""
    LOCAL_DATABASE = "local_database"       # Qdrant vector DB
    WEB_SEARCH = "web_search"              # Google search
    MEMORY_SYSTEM = "memory_system"        # Graphiti memory
    EXTERNAL_API = "external_api"          # Weather, time APIs

@dataclass
class RoutingDecision:
    """Result of query routing analysis."""
    query_type: QueryType
    primary_source: DataSource
    fallback_sources: List[DataSource]
    confidence: float
    reasoning: str
    detected_entities: Dict[str, List[str]]

class QueryRouter:
    """
    Intelligent router that determines the best data source for each query.
    
    This router scales with the system by:
    1. Maintaining a registry of supported cities/regions
    2. Using pattern matching and NLP to classify queries
    3. Providing fallback strategies when primary sources fail
    4. Learning from query patterns over time
    """
    
    def __init__(self):
        # Registry of supported cities and regions in our local database
        self.supported_locations = {
            'burnaby', 'new westminster', 'new west', 'vancouver', 'richmond',
            'surrey', 'north vancouver', 'west vancouver', 'coquitlam', 'langley',
            'delta', 'white rock', 'port moody', 'port coquitlam', 'maple ridge',
            'pitt meadows', 'anmore', 'belcarra', 'lions bay', 'bowen island'
        }
        
        # Activity-related keywords that indicate local database search
        self.activity_keywords = {
            'classes', 'activities', 'programs', 'camps', 'lessons', 'courses',
            'swimming', 'soccer', 'hockey', 'basketball', 'tennis', 'badminton',
            'art', 'music', 'dance', 'drama', 'theater', 'pottery', 'painting',
            'fitness', 'yoga', 'pilates', 'martial arts', 'karate', 'judo',
            'gymnastics', 'skating', 'skiing', 'snowboarding', 'climbing',
            'preschool', 'toddler', 'kids', 'children', 'youth', 'adult', 'senior',
            'registration', 'register', 'sign up', 'enroll', 'book', 'schedule'
        }
        
        # Age-related patterns
        self.age_patterns = [
            r'\b(\d+)\s*(?:year|yr)s?\s*old\b',
            r'\b(?:age|aged)\s*(\d+)\b',
            r'\b(\d+)\s*(?:-|to)\s*(\d+)\s*(?:year|yr)s?\b'
        ]
        
        # Web search indicators
        self.web_search_keywords = {
            'news', 'latest', 'current events', 'what happened', 'recent',
            'definition', 'what is', 'explain', 'history of', 'background',
            'weather', 'forecast', 'temperature', 'climate'
        }
        
        # Memory query indicators
        self.memory_keywords = {
            'remember', 'my name', 'told you', 'previous', 'last time',
            'my child', 'my kids', 'my preferences', 'what did i say'
        }
    
    def route_query(self, query: str, context: Optional[Dict] = None) -> RoutingDecision:
        """
        Main routing function that determines the best data source for a query.
        
        Args:
            query: User's query string
            context: Optional context (user history, session data, etc.)
            
        Returns:
            RoutingDecision with recommended routing strategy
        """
        query_lower = query.lower().strip()
        
        # Extract entities from the query
        entities = self._extract_entities(query_lower)
        
        # Determine query type and routing
        if self._is_activity_query(query_lower, entities):
            return self._route_activity_query(query, query_lower, entities)
        elif self._is_memory_query(query_lower):
            return self._route_memory_query(query, query_lower, entities)
        elif self._is_web_search_query(query_lower):
            return self._route_web_search_query(query, query_lower, entities)
        else:
            return self._route_general_query(query, query_lower, entities)
    
    def _extract_entities(self, query_lower: str) -> Dict[str, List[str]]:
        """Extract relevant entities from the query."""
        entities = {
            'locations': [],
            'ages': [],
            'activities': [],
            'time_references': []
        }
        
        # Extract locations
        for location in self.supported_locations:
            if location in query_lower:
                entities['locations'].append(location)
        
        # Extract ages
        for pattern in self.age_patterns:
            matches = re.findall(pattern, query_lower)
            for match in matches:
                if isinstance(match, tuple):
                    entities['ages'].extend(match)
                else:
                    entities['ages'].append(match)
        
        # Extract activities
        for activity in self.activity_keywords:
            if activity in query_lower:
                entities['activities'].append(activity)
        
        return entities
    
    def _is_activity_query(self, query_lower: str, entities: Dict) -> bool:
        """Determine if this is an activity-related query."""
        # Strong indicators for activity queries
        if entities['activities'] or entities['locations']:
            return True
        
        # Check for activity-related patterns
        activity_patterns = [
            r'\b(?:find|search|look for|need|want)\b.*\b(?:class|activity|program|camp|lesson)\b',
            r'\b(?:register|sign up|enroll|book)\b',
            r'\b(?:swimming|soccer|art|music|dance|fitness)\b.*\b(?:class|lesson|program)\b'
        ]
        
        for pattern in activity_patterns:
            if re.search(pattern, query_lower):
                return True
        
        return False
    
    def _is_memory_query(self, query_lower: str) -> bool:
        """Determine if this is a memory-related query."""
        return any(keyword in query_lower for keyword in self.memory_keywords)
    
    def _is_web_search_query(self, query_lower: str) -> bool:
        """Determine if this should be a web search."""
        return any(keyword in query_lower for keyword in self.web_search_keywords)
    
    def _route_activity_query(self, query: str, query_lower: str, entities: Dict) -> RoutingDecision:
        """Route activity-related queries."""
        confidence = 0.9  # High confidence for activity queries
        
        # Check if we have location coverage
        has_supported_location = bool(entities['locations'])
        
        if has_supported_location:
            reasoning = f"Activity query with supported location(s): {entities['locations']}"
            primary_source = DataSource.LOCAL_DATABASE
            fallback_sources = [DataSource.WEB_SEARCH]  # Fallback if no local results
        else:
            reasoning = "Activity query without specific location - checking local database first"
            primary_source = DataSource.LOCAL_DATABASE
            fallback_sources = [DataSource.WEB_SEARCH]
        
        return RoutingDecision(
            query_type=QueryType.ACTIVITY_SEARCH,
            primary_source=primary_source,
            fallback_sources=fallback_sources,
            confidence=confidence,
            reasoning=reasoning,
            detected_entities=entities
        )
    
    def _route_memory_query(self, query: str, query_lower: str, entities: Dict) -> RoutingDecision:
        """Route memory-related queries."""
        return RoutingDecision(
            query_type=QueryType.MEMORY_QUERY,
            primary_source=DataSource.MEMORY_SYSTEM,
            fallback_sources=[],
            confidence=0.8,
            reasoning="Query contains memory-related keywords",
            detected_entities=entities
        )
    
    def _route_web_search_query(self, query: str, query_lower: str, entities: Dict) -> RoutingDecision:
        """Route web search queries."""
        return RoutingDecision(
            query_type=QueryType.WEB_SEARCH,
            primary_source=DataSource.WEB_SEARCH,
            fallback_sources=[],
            confidence=0.8,
            reasoning="Query requires current/general information from web",
            detected_entities=entities
        )
    
    def _route_general_query(self, query: str, query_lower: str, entities: Dict) -> RoutingDecision:
        """Route general conversational queries."""
        return RoutingDecision(
            query_type=QueryType.CONVERSATION,
            primary_source=DataSource.LOCAL_DATABASE,  # Try local first
            fallback_sources=[DataSource.WEB_SEARCH],
            confidence=0.5,
            reasoning="General query - trying local database first with web fallback",
            detected_entities=entities
        )
    
    def add_supported_location(self, location: str):
        """Add a new supported location to the registry."""
        self.supported_locations.add(location.lower())
        logger.info(f"Added new supported location: {location}")
    
    def get_supported_locations(self) -> List[str]:
        """Get list of all supported locations."""
        return sorted(list(self.supported_locations))

# Global router instance
query_router = QueryRouter()
