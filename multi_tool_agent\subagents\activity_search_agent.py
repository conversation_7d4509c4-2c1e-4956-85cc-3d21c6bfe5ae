from google.adk.agents import Agent

# Relative imports for tools
from ..tools.activity_search_tools import extract_activity_filters
from ..unified_activity_search import get_activity_search

# -----------------------------------------------------------------------------
# Activity Search Specialist Agent
# -----------------------------------------------------------------------------

activity_search_agent = Agent(
    name="bc_activity_finder",
    model="gemini-2.0-flash",
    description=(
        "Specialized agent that finds and recommends children's activities, "
        "classes, or camps in British Columbia."
    ),
    instruction=(
        "You are a specialized AI agent for finding children's activities in British Columbia. Your goal is to use your tools to find relevant activities and return the structured data.\n\n"
        "**REASONING PROTOCOL:**\n"
        "1.  **Extract Filters:** Always start by using the `extract_activity_filters` tool on the user's query to get structured search criteria.\n"
        "2.  **Perform Search:** Use the filters from the previous step to call the `get_activity_search` tool.\n"
        "3.  **Return Results:**\n"
        "    - If the search is successful, return the `results` and `search_type` directly. Do not add conversational text.\n"
        "    - If the search tool returns an error or finds no results, return a status indicating failure (e.g., `{'status': 'error', 'message': 'Search failed or found no results.'}`). This allows the orchestrator to try a different approach."
    ),
    tools=[
        extract_activity_filters,
        get_activity_search,
    ],
)
