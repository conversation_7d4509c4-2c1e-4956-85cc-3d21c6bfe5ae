#!/usr/bin/env python3
"""
Enhanced Burnaby Activity Scraper with Browser Headers
=====================================================

This version mimics the exact browser request headers:
1. Includes page_info header with page number
2. Includes x-csrf-token header
3. Uses proper browser headers and cookies
4. Mimics exact browser behavior

Key features:
• Browser header replication
• CSRF token handling
• Page info header for pagination
• Automatic deduplication and stopping conditions
"""
from __future__ import annotations

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
import uuid

import httpx

# ---------------------------------------------------------------------------
# Logging setup
# ---------------------------------------------------------------------------
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
    encoding='utf-8'
)

# ---------------------------------------------------------------------------
# Constants
# ---------------------------------------------------------------------------
BASE_URL = "https://anc.ca.apm.activecommunities.com"
SEARCH_URL = f"{BASE_URL}/burnaby/activity/search?onlineSiteId=0&activity_select_param=2&viewMode=list"
REQUEST_DELAY = 2  # Increased delay to avoid server errors
NAMESPACE_UUID = uuid.UUID('9e71ea85-7976-4395-a78b-1616c689eea7')
_CUR_DIR = Path(__file__).parent

class EnhancedBurnabyActivityScraper:
    def __init__(self):
        self.client = None
        self.semaphore = asyncio.Semaphore(5)  # Limit concurrent requests
        self.session_stats = {"api_calls": 0, "total_activities": 0, "errors": 0, "start_time": time.time()}
        self.csrf_token = None
        self.session_cookies = {}

    async def __aenter__(self):
        self.client = httpx.AsyncClient(
            follow_redirects=True,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "*/*",
                "Accept-Language": "en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "DNT": "1",
                "Connection": "keep-alive",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Ch-Ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
            },
            timeout=30.0
        )
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.client.aclose()

    async def initialize_session(self):
        """Initialize session and extract CSRF token and cookies."""
        logger.info("Initializing session...")
        
        # First, get the search page to establish session
        response = await self.client.get(SEARCH_URL)
        response.raise_for_status()
        
        # Extract cookies
        for cookie in response.cookies:
            self.session_cookies[cookie.name] = cookie.value
        
        # Try to extract CSRF token from the page content
        # Look for common patterns in the HTML
        content = response.text
        csrf_patterns = [
            'csrf-token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'x-csrf-token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'csrf["\']?\s*[:=]\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in csrf_patterns:
            import re
            match = re.search(pattern, content)
            if match:
                self.csrf_token = match.group(1)
                logger.info(f"Found CSRF token: {self.csrf_token}")
                break
        
        # If no CSRF token found, use a default one (might work for some requests)
        if not self.csrf_token:
            self.csrf_token = "default-csrf-token"
            logger.warning("No CSRF token found, using default")
        
        logger.info("Session initialized")

    def _extract_activities_from_response(self, response_data: Dict[str, Any]) -> tuple[List[Dict[str, Any]], int, int]:
        """Extract activities and pagination info from API response."""
        try:
            # Extract pagination info from headers
            headers = response_data.get("headers", {})
            page_info = headers.get("page_info", {})
            total_records = page_info.get("total_records", 0)
            total_pages = page_info.get("total_page", 0)
            
            # Extract activities from body
            body = response_data.get("body", {})
            activity_items = body.get("activity_items", [])
            
            # Standardize activities
            activities = []
            for item in activity_items:
                standardized_activity = self._standardize_activity(item)
                if standardized_activity:
                    activities.append(standardized_activity)
            
            return activities, total_records, total_pages
        except Exception as e:
            logger.error(f"Error extracting activities: {e}")
            return [], 0, 0

    async def fetch_activities_with_page_info(self, page_number: int = 1, retries: int = 3) -> tuple[List[Dict[str, Any]], int, int]:
        """Fetch activities using page_info header like the browser."""
        for attempt in range(retries):
            async with self.semaphore:
                try:
                    await asyncio.sleep(REQUEST_DELAY)  # Respect rate limiting
                    
                    api_url = f"{BASE_URL}/burnaby/rest/activities/list"
                    
                    # Use the same payload as before
                    payload = {
                        "activity_search_pattern": {
                            "skills": [],
                            "time_after_str": "",
                            "days_of_week": None,
                            "activity_select_param": 2,
                            "center_ids": [],
                            "time_before_str": "",
                            "open_spots": None,
                            "activity_id": None,
                            "activity_category_ids": [],
                            "date_before": "",
                            "min_age": None,
                            "date_after": "",
                            "activity_type_ids": [],
                            "site_ids": [],
                            "for_map": False,
                            "geographic_area_ids": [],
                            "season_ids": [],
                            "activity_department_ids": [],
                            "activity_other_category_ids": [],
                            "child_season_ids": [],
                            "activity_keyword": "",
                            "instructor_ids": [],
                            "max_age": None,
                            "custom_price_from": "",
                            "custom_price_to": ""
                        },
                        "activity_transfer_pattern": {}
                    }

                    # Create headers that match the browser request
                    headers = {
                        "Content-Type": "application/json;charset=utf-8",
                        "Accept": "*/*",
                        "Accept-Language": "en-US,en;q=0.9,zh-TW;q=0.8,zh;q=0.7",
                        "Accept-Encoding": "gzip, deflate, br, zstd",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Host": "anc.ca.apm.activecommunities.com",
                        "Origin": "https://anc.ca.apm.activecommunities.com",
                        "Referer": SEARCH_URL,
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-origin",
                        "Sec-Ch-Ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                        "Sec-Ch-Ua-Mobile": "?0",
                        "Sec-Ch-Ua-Platform": '"Windows"',
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        "X-Requested-With": "XMLHttpRequest",
                        "X-CSRF-Token": self.csrf_token,
                        "page_info": json.dumps({
                            "order_by": "Date range",
                            "page_number": page_number,
                            "total_records_per_page": 20
                        })
                    }

                    resp = await self.client.post(
                        api_url,
                        params={"locale": "en-US"},
                        json=payload,
                        headers=headers
                    )
                    
                    if resp.status_code == 200:
                        data = resp.json()
                        
                        activities, total_records, total_pages = self._extract_activities_from_response(data)
                        self.session_stats["api_calls"] += 1
                        self.session_stats["total_activities"] += len(activities)
                        logger.info(f"Page {page_number}: {len(activities)} activities (Total records: {total_records})")
                        return activities, total_records, total_pages
                    else:
                        logger.warning(f"API returned status {resp.status_code}. Response: {resp.text[:200]}")
                        if resp.status_code == 500 and attempt < retries - 1:
                            logger.info(f"Retrying page {page_number} (attempt {attempt + 2}/{retries})...")
                            # Reinitialize session
                            await self.initialize_session()
                            continue
                        return [], 0, 0
                    
                except Exception as e:
                    logger.error(f"Error fetching page {page_number}: {e}")
                    self.session_stats["errors"] += 1
                    if attempt < retries - 1:
                        logger.info(f"Retrying page {page_number} (attempt {attempt + 2}/{retries})...")
                        await asyncio.sleep(REQUEST_DELAY * (attempt + 1))
                        continue
                    return [], 0, 0

    def _standardize_activity(self, raw_activity: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Convert raw activity data to standardized format with unique record_id."""
        try:
            activity_id = raw_activity.get("id")
            name = raw_activity.get("name")
            
            if not activity_id or not name:
                logger.debug(f"Skipping activity with missing id or name: {raw_activity}")
                return None
                
            # Generate a simpler, more reliable record_id
            # Use activity ID as primary identifier, with optional activity number for uniqueness
            activity_number = raw_activity.get("number", "")
            if activity_number:
                unique_id_str = f"burnaby-{activity_id}-{activity_number}"
            else:
                unique_id_str = f"burnaby-{activity_id}"
            
            record_id = str(uuid.uuid5(NAMESPACE_UUID, unique_id_str))

            # Log for debugging duplicates
            logger.debug(f"Generated record_id: {record_id} for activity: {name} (ID: {activity_id})")

            # Extract price
            price_label = raw_activity.get("fee", {}).get("label")
            price_numeric = self._parse_price_burnaby(price_label)

            # Extract age info
            age_info_parsed = self._parse_age_range_burnaby(raw_activity)

            # Extract time info
            time_info_parsed = self._parse_time_range_burnaby(raw_activity.get("time_range"))

            # Determine registration status
            status_description = raw_activity.get("urgent_message", {}).get("status_description", "").lower()
            openings = raw_activity.get("openings")
            
            registration_status = "unknown"
            is_full = False
            if status_description == "full" or (isinstance(openings, str) and openings == "0"):
                registration_status = "full"
                is_full = True
            elif status_description == "in progress" or (isinstance(openings, (str, int)) and int(openings) > 0):
                registration_status = "open"
            elif "waitlist" in status_description:
                registration_status = "waitlist"
            
            # General location mapping (based on 'site' field)
            site_name = raw_activity.get("site", "")
            general_location = site_name
            if "Edmonds Community Centre" in site_name:
                general_location = "Edmonds Community Centre (ECC)"
            elif "Christine Sinclair Community Centre" in site_name:
                general_location = "Christine Sinclair Community Centre (CSC)"
            elif "Temporary Cameron Community Centre" in site_name:
                general_location = "Temporary Cameron Community Centre & Library (CAM)"
            elif "Eileen Dailly Leisure Pool" in site_name:
                general_location = "Eileen Dailly Leisure Pool & Fitness Centre (EDP)"

            # Days of week list for Qdrant filtering
            days_of_week_raw = raw_activity.get("days_of_week", "")
            days_of_week_list = []
            if days_of_week_raw:
                day_mapping = {
                    "mon": "monday", "tue": "tuesday", "wed": "wednesday",
                    "thu": "thursday", "fri": "friday", "sat": "saturday", "sun": "sunday"
                }
                for abbr, full_name in day_mapping.items():
                    if abbr.lower() in days_of_week_raw.lower():
                        days_of_week_list.append(full_name)
                # Handle "Every Mon, Tue, Wed" format
                if "every" in days_of_week_raw.lower():
                    for day_abbr in ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]:
                        if day_abbr in days_of_week_raw.lower():
                            days_of_week_list.append(day_mapping[day_abbr])
                days_of_week_list = sorted(list(set(days_of_week_list))) # Deduplicate and sort

            return {
                "record_id": record_id,
                "name": name,
                "description": raw_activity.get("description", ""),
                "city": "burnaby",  # Add normalized city field for robust filtering
                "category": raw_activity.get("category", ""),
                "activity_url": raw_activity.get("detail_url"),
                "general_location": general_location,
                "location": site_name, # More specific location
                "facility": site_name,
                "age_info": raw_activity.get("age_description"),
                "min_age_years": age_info_parsed["min_age_years"],
                "max_age_years": age_info_parsed["max_age_years"],
                "start_date": raw_activity.get("date_range_start"),
                "end_date": raw_activity.get("date_range_end"),
                "start_time": raw_activity.get("time_range"),
                "end_time": raw_activity.get("time_range"), # Burnaby API often has same start/end time range
                "start_time_iso": time_info_parsed["start_time_iso"],
                "end_time_iso": time_info_parsed["end_time_iso"],
                "duration_minutes": time_info_parsed["duration_minutes"],
                "days_of_week": days_of_week_raw,
                "days_of_week_list": days_of_week_list, # For Qdrant filtering
                "day_of_week": days_of_week_list[0] if days_of_week_list else None, # For single day filtering
                "price": price_label,
                "price_numeric": price_numeric,
                "is_full": is_full,
                "registration_status": registration_status,
                "openings": raw_activity.get("openings"),
                "source": "Burnaby ActiveCommunities",
                "raw_api_data": raw_activity # Keep original for debugging
            }
            
        except Exception as e:
            logger.debug(f"Failed to standardize activity: {e}")
            return None

    def _parse_age_range_burnaby(self, raw_activity: Dict[str, Any]) -> Dict[str, Optional[int]]:
        """Parses age range from Burnaby's specific age fields."""
        min_year = raw_activity.get("age_min_year")
        max_year = raw_activity.get("age_max_year")
        min_month = raw_activity.get("age_min_month")
        max_month = raw_activity.get("age_max_month")

        min_age_years = None
        if min_year is not None and min_year > 0:
            min_age_years = min_year
        elif min_month is not None and min_month > 0:
            min_age_years = 0 # Representing months as part of the first year

        max_age_years = None
        if max_year is not None and max_year > 0:
            max_age_years = max_year
        elif max_month is not None and max_month > 0:
            max_age_years = 0 # Representing months as part of the first year
        
        # Handle cases like "19 yrs +" where max_year is 0
        if min_age_years is not None and max_age_years == 0:
            max_age_years = 99 # Assume adult range

        return {"min_age_years": min_age_years, "max_age_years": max_age_years}

    def _parse_time_range_burnaby(self, time_str: Optional[str]) -> Dict[str, Any]:
        """Parses time range from Burnaby's 'HH:MM AM/PM - HH:MM AM/PM' format."""
        if not time_str:
            return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}

        parts = [p.strip() for p in time_str.split('-') if p.strip()]
        if len(parts) != 2:
            return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}

        try:
            from datetime import datetime
            start_dt = datetime.strptime(parts[0], "%I:%M %p")
            end_dt = datetime.strptime(parts[1], "%I:%M %p")
            
            duration = (end_dt - start_dt).total_seconds() / 60
            if duration < 0: # Handle overnight activities
                duration += 24 * 60

            return {
                "start_time_iso": start_dt.strftime("%H:%M:%S"),
                "end_time_iso": end_dt.strftime("%H:%M:%S"),
                "duration_minutes": int(duration)
            }
        except ValueError:
            return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}

    def _parse_price_burnaby(self, price_str: Optional[str]) -> Optional[float]:
        """Extracts numeric price from a string like '$75.90' or 'View fee details'."""
        if not price_str:
            return None
        
        # Remove currency symbols and commas, then try to convert to float
        clean_price_str = price_str.replace('$', '').replace(',', '').strip()
        try:
            return float(clean_price_str)
        except ValueError:
            return None # Return None if it's not a valid number (e.g., "View fee details")

    async def crawl_all_activities(self) -> List[Dict[str, Any]]:
        """Crawl all activities using page_info header pagination."""
        logger.info("Starting browser-mimicking crawl")

        # Initialize session and get CSRF token
        await self.initialize_session()

        all_activities = []
        seen_ids = set()
        page = 1
        total_pages = 1  # Will be updated after first request
        total_records = 0
        consecutive_duplicates = 0
        max_consecutive_duplicates = 5

        while page <= total_pages and consecutive_duplicates < max_consecutive_duplicates:
            batch, total_records, total_pages = await self.fetch_activities_with_page_info(page_number=page)
            if not batch:
                logger.warning(f"No activities returned for page {page}")
                consecutive_duplicates += 1
                page += 1
                continue

            new_count = 0
            duplicate_count = 0
            for activity in batch:
                rid = activity.get("record_id")
                if rid and rid not in seen_ids:
                    seen_ids.add(rid)
                    all_activities.append(activity)
                    new_count += 1
                else:
                    duplicate_count += 1

            logger.info(f"Page {page}/{total_pages}: Added {new_count} new activities, {duplicate_count} duplicates (Total: {len(all_activities)})")
            
            if new_count == 0:
                consecutive_duplicates += 1
                logger.info(f"Consecutive duplicates: {consecutive_duplicates}/{max_consecutive_duplicates}")
            else:
                consecutive_duplicates = 0  # Reset counter if we found new activities
            
            if page == 1:
                logger.info(f"Found {total_records} total activities across {total_pages} pages")
            
            page += 1

        # Final stats
        elapsed = time.time() - self.session_stats["start_time"]
        logger.info("\nCrawl complete!")
        logger.info(f"   * Total unique activities: {len(all_activities)}")
        logger.info(f"   * API calls made: {self.session_stats['api_calls']}")
        logger.info(f"   * Errors: {self.session_stats['errors']}")
        logger.info(f"   * Time elapsed: {elapsed:.1f} seconds")

        return all_activities

# Example usage
async def run_scraper():
    async with EnhancedBurnabyActivityScraper() as scraper:
        activities = await scraper.crawl_all_activities()
        return activities

# ---------------------------------------------------------------------------
# Command-line interface
# ---------------------------------------------------------------------------
async def main():
    """Main function to run the enhanced scraper."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced Burnaby Activity Scraper")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging.")
    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)
    logger.info("Enhanced Burnaby Activity Scraper Starting...")

    try:
        async with EnhancedBurnabyActivityScraper() as scraper:
            activities = await scraper.crawl_all_activities()

            if activities:
                # Save results
                output_file = _CUR_DIR / "burnaby_activities_complete.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(activities, f, indent=2, ensure_ascii=False, default=str)

                logger.info(f"Saved {len(activities)} activities to {output_file}")

                # Show sample results
                logger.info("\nSample activities:")
                for i, activity in enumerate(activities[:5], 1):
                    logger.info(f"  {i}. {activity.get('name', 'Unknown')} ({activity.get('category', 'No category')})")

                if len(activities) > 5:
                    logger.info(f"  ... and {len(activities) - 5} more")
            else:
                logger.warning("No activities found")

    except KeyboardInterrupt:
        logger.info("\nStopped by user")
    except Exception as e:
        logger.exception("Fatal error:")

if __name__ == "__main__":
    asyncio.run(main())
