#!/usr/bin/env python3

import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_tool_agent.unified_activity_search import _matches_activity_filter, _normalize_payload

def test_age_filtering():
    """Test the age filtering fix for open-ended age ranges like '3+'"""
    
    print("🧪 TESTING AGE FILTERING FIX")
    print("=" * 40)
    
    # Test data similar to the example provided
    test_activity_raw = {
        "metadata": {
            "record_id": "2c705b3f-be45-52c4-acb7-a6e6e99afda2",
            "name": "Swimming: Private or Semi-Private Lesson",
            "description": "",
            "category": "Swimming",
            "activity_url": "https://ca.apm.activecommunities.com/burnaby/Activity_Search/swimming-private-or-semi-private-lesson/66717?locale=en-US",
            "general_location": "Eileen Dailly Leisure Pool & Fitness Centre (EDP)",
            "location": "Eileen Dailly Leisure Pool & Fitness Centre (EDP)",
            "facility": "Eileen Dailly Leisure Pool & Fitness Centre (EDP)",
            "age_info": "3 yrs +,",
            "min_age_years": 3,
            "max_age_years": None,  # This is the key issue - null for open-ended ranges
            "source": "Burnaby ActiveCommunities",
            "price": "View fee details",
            "registration_status": "full"
        }
    }
    
    # Normalize the payload
    normalized_activity = _normalize_payload(test_activity_raw)
    
    print("📋 NORMALIZED ACTIVITY:")
    print(f"  Name: {normalized_activity['name']}")
    print(f"  Category: {normalized_activity['category']}")
    print(f"  Age Info: {normalized_activity['age_info']}")
    print(f"  Min Age: {normalized_activity['min_age']}")
    print(f"  Max Age: {normalized_activity['max_age']}")
    print()
    
    # Test age filtering for different ages
    test_ages = [2, 3, 5, 8, 12, 15]
    
    print("🔍 TESTING AGE FILTERING:")
    print("-" * 25)
    
    for age in test_ages:
        activity_filter = {
            "age": age,
            "categories": ["Swimming"]
        }
        
        matches = _matches_activity_filter(normalized_activity, activity_filter)
        
        expected = age >= 3  # Should match for age 3 and above
        status = "✅ PASS" if matches == expected else "❌ FAIL"
        
        print(f"  Age {age}: {matches} (expected: {expected}) {status}")
    
    print()
    
    # Test specific case: 5-year-old looking for swimming
    print("🎯 SPECIFIC TEST: 5-year-old swimming")
    print("-" * 35)
    
    five_year_old_filter = {
        "age": 5,
        "categories": ["Swimming"]
    }
    
    matches = _matches_activity_filter(normalized_activity, five_year_old_filter)
    
    print(f"  Activity: {normalized_activity['name']}")
    print(f"  Age Range: {normalized_activity['age_info']}")
    print(f"  Min Age: {normalized_activity['min_age']}")
    print(f"  Max Age: {normalized_activity['max_age']}")
    print(f"  5-year-old matches: {matches}")
    
    if matches:
        print("  🎉 SUCCESS: 5-year-old can access '3+' activity!")
    else:
        print("  ❌ FAILED: 5-year-old cannot access '3+' activity")
    
    print()
    
    # Test edge cases
    print("🔬 EDGE CASE TESTS:")
    print("-" * 20)
    
    edge_cases = [
        {
            "name": "Exact minimum age",
            "age": 3,
            "expected": True
        },
        {
            "name": "Below minimum age", 
            "age": 2,
            "expected": False
        },
        {
            "name": "Well above minimum",
            "age": 10,
            "expected": True
        }
    ]
    
    for case in edge_cases:
        filter_obj = {"age": case["age"], "categories": ["Swimming"]}
        result = _matches_activity_filter(normalized_activity, filter_obj)
        status = "✅ PASS" if result == case["expected"] else "❌ FAIL"
        print(f"  {case['name']} (age {case['age']}): {result} {status}")

if __name__ == "__main__":
    test_age_filtering()
