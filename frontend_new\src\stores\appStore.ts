import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { 
  Message, 
  ToolStatus, 
  Child, 
  Preference, 
  Registration, 
  Activity, 
  MemoryItem, 
  UIError 
} from '../types/adk';
import { API_BASE_URL } from '../config';

// Authentication types
interface GoogleUser {
  google_id: string;
  email: string;
  name: string;
  given_name: string;
  family_name: string;
  picture?: string;
  email_verified: boolean;
}

interface AuthState {
  isAuthenticated: boolean;
  user: GoogleUser | null;
  token: string | null;
  expires_at: number | null;
  isLoading: boolean;
}

interface AppState {
  // Authentication
  auth: AuthState;
  
  // Session Management
  sessionId: string;
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  
  // Conversation
  messages: Message[];
  isAgentTyping: boolean;
  currentTool: ToolStatus | null;
  
  // Memory/Profile
  childProfiles: Child[];
  activityPreferences: Preference[];
  storedMemories: MemoryItem[];
  
  // Activities
  discoveredActivities: Activity[];
  registrationHistory: Registration[];
  
  // UI State
  sidebarOpen: boolean;
  activePanel: 'chat' | 'memory' | 'activities';
  errors: UIError[];
  
  // Actions
  // Authentication actions
  signIn: (googleToken: string) => Promise<void>;
  signOut: () => void;
  checkAuthStatus: () => void;
  
  setSessionId: (id: string) => void;
  setConnectionStatus: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void;
  addMessage: (message: Message) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  setCurrentTool: (tool: ToolStatus | null) => void;
  setIsAgentTyping: (typing: boolean) => void;
  addActivity: (activity: Activity) => void;
  addMemoryItem: (item: MemoryItem) => void;
  addError: (error: UIError) => void;
  clearErrors: () => void;
  toggleSidebar: () => void;
  setActivePanel: (panel: 'chat' | 'memory' | 'activities') => void;
  clearSession: () => void;
}

export const useAppStore = create<AppState>()(
  subscribeWithSelector((set, get) => ({
    // Authentication initial state
    auth: {
      isAuthenticated: false,
      user: null,
      token: null,
      expires_at: null,
      isLoading: false,
    },
    
    // Authentication actions
    signIn: async (googleToken: string) => {
      set(state => ({
        auth: { ...state.auth, isLoading: true }
      }));
      
      try {
        const response = await fetch(`${API_BASE_URL}/auth/google`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ google_token: googleToken }),
        });
        
        if (!response.ok) {
          throw new Error('Authentication failed');
        }
        
        const authResult = await response.json();
        
        // Store token in localStorage
        localStorage.setItem('auth_token', authResult.session_token);
        localStorage.setItem('auth_expires', authResult.expires_at.toString());
        localStorage.setItem('user_profile', JSON.stringify(authResult.user_profile));
        
        set(() => ({
          auth: {
            isAuthenticated: true,
            user: authResult.user_profile,
            token: authResult.session_token,
            expires_at: authResult.expires_at,
            isLoading: false,
          }
        }));
        
        console.log('✅ Authentication successful:', authResult.user_profile.name);
        
      } catch (error) {
        console.error('❌ Authentication failed:', error);
        set((state) => ({
          auth: { ...state.auth, isLoading: false }
        }));
        throw error;
      }
    },
    
    signOut: () => {
      // Clear localStorage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_expires');
      localStorage.removeItem('user_profile');
      
      // Clear state
      set(() => ({
        auth: {
          isAuthenticated: false,
          user: null,
          token: null,
          expires_at: null,
          isLoading: false,
        },
        messages: [],
        discoveredActivities: [],
        storedMemories: [],
        childProfiles: [],
        activityPreferences: [],
        registrationHistory: [],
      }));
      
      console.log('🚪 User signed out');
    },
    
    checkAuthStatus: () => {
      const token = localStorage.getItem('auth_token');
      const expires = localStorage.getItem('auth_expires');
      const userProfile = localStorage.getItem('user_profile');
      
      if (token && expires && userProfile) {
        const expiresAt = parseInt(expires);
        const now = Math.floor(Date.now() / 1000);
        
        if (expiresAt > now) {
          // Token is still valid
          set(() => ({
            auth: {
              isAuthenticated: true,
              user: JSON.parse(userProfile),
              token: token,
              expires_at: expiresAt,
              isLoading: false,
            }
          }));
          console.log('✅ Auth restored from localStorage');
        } else {
          // Token expired
          get().signOut();
          console.log('⏰ Token expired, signed out');
        }
      } else {
        console.log('❌ No valid auth found in localStorage');
      }
    },
    
    // Initial State
    sessionId: '',
    isConnected: false,
    connectionStatus: 'disconnected',
    
    messages: [],
    isAgentTyping: false,
    currentTool: null,
    
    childProfiles: [],
    activityPreferences: [],
    storedMemories: [],
    
    discoveredActivities: [],
    registrationHistory: [],
    
    sidebarOpen: false,
    activePanel: 'chat',
    errors: [],
    
    // Actions
    setSessionId: (id: string) => {
      set({ sessionId: id });
    },
    
    setConnectionStatus: (status) => {
      set({ 
        connectionStatus: status,
        isConnected: status === 'connected'
      });
    },
    
    addMessage: (message: Message) => {
      set(state => ({
        messages: [...state.messages, message]
      }));
    },
    
    updateMessage: (id: string, updates: Partial<Message>) => {
      set(state => ({
        messages: state.messages.map(msg => 
          msg.id === id ? { ...msg, ...updates } : msg
        )
      }));
    },
    
    setCurrentTool: (tool: ToolStatus | null) => {
      set({ currentTool: tool });
    },
    
    setIsAgentTyping: (typing: boolean) => {
      set({ isAgentTyping: typing });
    },
    
    addActivity: (activity: Activity) => {
      set(state => {
        // Avoid duplicates based on name and provider
        const exists = state.discoveredActivities.some(
          a => a.name === activity.name && a.provider === activity.provider
        );
        
        if (!exists) {
          return {
            discoveredActivities: [...state.discoveredActivities, activity]
          };
        }
        return {};
      });
    },
    
    addMemoryItem: (item: MemoryItem) => {
      set(state => ({
        storedMemories: [...state.storedMemories, item]
      }));
    },
    
    addError: (error: UIError) => {
      set(state => ({
        errors: [...state.errors, error]
      }));
    },
    
    clearErrors: () => {
      set({ errors: [] });
    },
    
    toggleSidebar: () => {
      set(state => ({ sidebarOpen: !state.sidebarOpen }));
    },
    
    setActivePanel: (panel: 'chat' | 'memory' | 'activities') => {
      set({ activePanel: panel });
    },
    
    clearSession: () => {
      set({
        messages: [],
        isAgentTyping: false,
        currentTool: null,
        discoveredActivities: [],
        // Keep profile/memory data
      });
    },
  }))
);

// Export authentication types for use in components
export type { GoogleUser, AuthState };

// Utility function to generate session ID
export const generateSessionId = (): string => {
  return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Utility function to create message
export const createMessage = (
  role: 'user' | 'assistant', 
  content: string, 
  isComplete: boolean = true
): Message => ({
  id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  role,
  content,
  timestamp: Date.now(),
  isComplete
});

// Utility function to create error
export const createError = (
  message: string, 
  type: 'connection' | 'session' | 'general' = 'general'
): UIError => ({
  id: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  message,
  timestamp: Date.now(),
  type
});
