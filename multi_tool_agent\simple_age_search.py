#!/usr/bin/env python3

import re
import logging
from typing import Dict, List
from multi_tool_agent.config import AgentConfig
from qdrant_client import AsyncQdrantClient
import google.generativeai as genai

# For type hinting - will work regardless of ADK availability
try:
    from google.genai.adk import ToolContext
except ImportError:
    ToolContext = object

logger = logging.getLogger(__name__)

async def get_simple_age_search(query: str, tool_context: ToolContext) -> Dict:
    """
    Simple, reliable activity search focused on age appropriateness.
    No complex LLM parsing - just solid age filtering and diversity.
    """
    
    try:
        # Initialize clients
        qdrant_client = AsyncQdrantClient(
            url=AgentConfig.QDRANT_URL,
            api_key=AgentConfig.QDRANT_API_KEY
        )
        
        genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
        
        # Extract age from query using regex (reliable)
        child_age = _extract_age_from_query(query)
        logger.info(f"Extracted age: {child_age} from query: '{query}'")
        
        # Search with broad terms to get diverse results
        search_terms = [
            f"children activities classes programs {child_age} year old",
            "kids swimming sports arts music dance programs",
            "preschool elementary children classes activities",
            f"age {child_age} activities programs classes"
        ]
        
        all_results = []
        
        for search_term in search_terms:
            # Generate embedding
            embedding_response = genai.embed_content(
                model="models/text-embedding-004",
                content=search_term
            )
            query_vector = embedding_response['embedding']
            
            # Search Qdrant
            results = await qdrant_client.search(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                query_vector=query_vector,
                limit=12,
                with_payload=True
            )
            
            all_results.extend(results)
        
        # Remove duplicates
        unique_results = _remove_duplicates(all_results)
        
        # Filter for age appropriateness with STRICT filtering
        if child_age:
            age_filtered = []
            for result in unique_results:
                payload = result.payload
                age_info = payload.get('age_info', '')
                
                if _is_strictly_age_appropriate(age_info, child_age):
                    age_filtered.append(result)
                else:
                    logger.debug(f"REJECTED age {age_info} for child age {child_age}: {payload.get('name', 'N/A')}")
            
            logger.info(f"Age filtering: {len(unique_results)} -> {len(age_filtered)} results")
            filtered_results = age_filtered
        else:
            filtered_results = unique_results
        
        # Ensure diversity (not all one category)
        diverse_results = _ensure_category_diversity(filtered_results)
        
        # If we found very few age-appropriate results, that's better than inappropriate ones
        if len(diverse_results) == 0:
            return {
                "status": "no_age_appropriate",
                "message": f"I couldn't find activities specifically for {child_age}-year-olds in our database.",
                "suggestion": f"Try searching for broader age ranges or specific activity types like 'swimming for kids under 6'",
                "guidance": "Let me know what type of activity you're most interested in (swimming, art, sports, etc.)"
            }
        
        # Format results
        formatted_results = []
        for result in diverse_results[:6]:  # Limit to top 6
            formatted_results.append(_format_activity(result))
        
        return {
            "status": "success",
            "count": len(formatted_results),
            "results": formatted_results,
            "age_targeted": child_age,
            "message": f"Found {len(formatted_results)} age-appropriate activities for {child_age}-year-olds"
        }
        
    except Exception as e:
        logger.error(f"Simple age search failed: {e}")
        return {
            "status": "error",
            "message": f"Search error: {e}",
            "fallback_suggestion": "Try searching for specific activity types"
        }

def _extract_age_from_query(query: str) -> int:
    """Extract child age from query using regex."""
    
    age_patterns = [
        r'(\d+)\s*year\s*old',
        r'age\s*(\d+)',
        r'(\d+)\s*yr',
        r'my\s*(\d+)',
        r'for\s*(\d+)'
    ]
    
    for pattern in age_patterns:
        match = re.search(pattern, query, re.IGNORECASE)
        if match:
            age = int(match.group(1))
            if 1 <= age <= 18:  # Reasonable age range
                return age
    
    return None

def _is_strictly_age_appropriate(age_info: str, target_age: int) -> bool:
    """
    STRICT age filtering - only return activities that explicitly include the target age.
    """
    
    if not age_info or not target_age:
        return False
    
    age_info_clean = age_info.strip().lower()
    
    # Pattern for age ranges with spaces: "(4 - 6)", "(3 - 5)"
    range_pattern = r'\((\d+)\s*-\s*(\d+)\)'
    match = re.search(range_pattern, age_info_clean)
    
    if match:
        min_age = int(match.group(1))
        max_age = int(match.group(2))
        
        # STRICT: target age must be within range
        is_appropriate = min_age <= target_age <= max_age
        
        if not is_appropriate:
            logger.debug(f"Age {target_age} not in range {min_age}-{max_age}")
        
        return is_appropriate
    
    # For now, be very strict - only accept clear ranges
    return False

def _remove_duplicates(results: List) -> List:
    """Remove duplicate activities based on name."""
    
    seen_names = set()
    unique = []
    
    for result in results:
        payload = result.payload
        name = payload.get('name', '')
        
        if name and name not in seen_names:
            seen_names.add(name)
            unique.append(result)
    
    return unique

def _ensure_category_diversity(results: List) -> List:
    """Ensure we don't return only swimming classes."""
    
    if len(results) <= 3:
        return results
    
    # Group by category
    by_category = {}
    for result in results:
        payload = result.payload
        category = payload.get('category', 'Unknown')
        
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(result)
    
    # Take max 2 from each category to ensure diversity
    diverse = []
    for category, category_results in by_category.items():
        diverse.extend(category_results[:2])
    
    logger.info(f"Category diversity: {list(by_category.keys())}")
    return diverse

def _format_activity(result) -> str:
    """Format activity for display."""
    
    payload = result.payload
    
    return (
        f"Activity: {payload.get('name', 'N/A')}\n"
        f"  Category: {payload.get('category', 'N/A')}\n"
        f"  Age Range: {payload.get('age_info', 'N/A')}\n"
        f"  Schedule: {payload.get('days_of_week', 'N/A')} {payload.get('start_time', 'N/A')}\n"
        f"  Facility: {payload.get('facility', 'N/A')}\n"
        f"  Dates: {payload.get('start_date', 'N/A')}\n"
        f"  Registration: {payload.get('activity_url', 'N/A')}"
    ) 