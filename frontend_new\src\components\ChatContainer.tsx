import { useState } from 'react';
import { Send } from 'lucide-react';
import { useAppStore } from '../stores/appStore';
import { useWebSocketConnection } from '../hooks/useWebSocketConnection';
import MessageList from './MessageList';
import ToolStatus from './ToolStatus';

const ChatContainer = () => {
  const [input, setInput] = useState('');
  const { sendMessage, isConnected } = useWebSocketConnection();
  const { currentTool } = useAppStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || !isConnected) return;

    const success = await sendMessage(input.trim());
    if (success) {
      setInput('');
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        <MessageList />
      </div>

      {/* Tool Status */}
      {currentTool && (
        <div className="px-4 py-2 border-t border-gray-200 bg-gray-50">
          <ToolStatus 
            isActive={!!currentTool}
            tool={currentTool.name}
            message={currentTool.message}
            estimatedDuration={currentTool.estimatedDuration}
          />
        </div>
      )}

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={isConnected ? "Ask about activities in BC..." : "Connecting to server..."}
            disabled={!isConnected}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500"
          />
          <button
            type="submit"
            disabled={!input.trim() || !isConnected}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            aria-label="Send message"
          >
            <Send className="w-5 h-5" />
          </button>
        </form>
        
        {!isConnected && (
          <p className="text-xs text-gray-500 mt-2">
            Waiting for connection to BC Activity Assistant...
          </p>
        )}
      </div>
    </div>
  );
};

export default ChatContainer; 