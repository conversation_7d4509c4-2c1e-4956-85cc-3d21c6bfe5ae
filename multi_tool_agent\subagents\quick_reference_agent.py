from google.adk.agents import Agent

from ..tools.general_tools import get_current_time, get_weather

# -----------------------------------------------------------------------------
# Quick reference agent (custom tools only – no built-ins)
# -----------------------------------------------------------------------------

quick_reference_agent = Agent(
    name="quick_reference_assistant",
    model="gemini-2.5-flash",
    description="Provides fast answers for current time in a timezone or weather in major BC cities.",
    instruction=(
        "You answer ONLY time or weather questions using your tools. "
        "• For weather queries call `get_weather`. "
        "• For time queries call `get_current_time`. "
        "Respond concisely."
    ),
    tools=[get_current_time, get_weather],
) 