import re
from datetime import datetime, timezone
from typing import Dict, Any

def _parse_date(date_val):
    """
    Parse various date formats and return standardized ISO format (YYYY-MM-DD).
    Handles formats from different scrapers and APIs.
    """
    if not date_val or not isinstance(date_val, str):
        return None

    # Handle /Date(XXXXXXXXXXXX)/ timestamp format from PerfectMind API
    m = re.match(r"/Date\((\d+)\)/", date_val)
    if m:
        try:
            ts = int(m.group(1)) // 1000
            return datetime.fromtimestamp(ts, tz=timezone.utc).strftime("%Y-%m-%d")
        except Exception:
            return None

    # Handle DD-MMM-YYYY format (e.g., "28-Jul-2025" from our scrapers)
    try:
        return datetime.strptime(date_val, "%d-%b-%Y").strftime("%Y-%m-%d")
    except Exception:
        pass

    # Handle YYYY-MM-DD format (already ISO)
    try:
        return datetime.strptime(date_val, "%Y-%m-%d").strftime("%Y-%m-%d")
    except Exception:
        pass

    # Handle DD/MM/YYYY format
    try:
        return datetime.strptime(date_val, "%d/%m/%Y").strftime("%Y-%m-%d")
    except Exception:
        pass

    # Handle MM/DD/YYYY format
    try:
        return datetime.strptime(date_val, "%m/%d/%Y").strftime("%Y-%m-%d")
    except Exception:
        pass

    # Handle DD-MM-YYYY format
    try:
        return datetime.strptime(date_val, "%d-%m-%Y").strftime("%Y-%m-%d")
    except Exception:
        pass

    # If none of the formats match, return None
    return None

def normalize_activity(activity: Dict[str, Any]) -> Dict[str, Any]:
    """
    Takes a raw activity from the JSONL file and returns a standardized, clean dictionary.
    This function will also infer the city from the facility and general_location fields.
    """
    # Basic normalization
    normalized = {
        "record_id": activity.get("record_id"),
        "name": activity.get("name"),
        "activity_url": activity.get("activity_url"),
        "category": activity.get("category"),
        "age_info": activity.get("age_info"),
        "min_age_years": activity.get("min_age_years"),
        "max_age_years": activity.get("max_age_years"),
        "start_time_iso": activity.get("start_time_iso"),
        "end_time_iso": activity.get("end_time_iso"),
        "days_of_week": activity.get("days_of_week"),
        "price_numeric": activity.get("price_numeric"),
        "is_open": activity.get("registration_status") == "open",
        "start_date": _parse_date(activity.get("start_date")),
        "end_date": _parse_date(activity.get("end_date")),
        # Handle program-level dates vs individual occurrence dates
        "event_start_date": _parse_date(
            activity.get("event_start_date") or
            activity.get("EventCurrentStartInJsFormat") or
            activity.get("EventDate") or
            activity.get("start_date")
        ),
        "event_end_date": _parse_date(
            activity.get("event_end_date") or
            activity.get("end_date") or
            activity.get("EventCurrentStartInJsFormat") or  # For single-day events, use start date
            activity.get("start_date")
            # Note: Intentionally not using EventEnd timestamp as it often represents session end time, not date
        ),
        "facility": activity.get("facility"),
        "general_location": activity.get("general_location"),
    }

    # Use existing city if set, otherwise infer from facility, general_location, or source
    existing_city = activity.get("city", "").lower()
    facility = activity.get("facility", "").lower()
    general_location = activity.get("general_location", "").lower()
    source = activity.get("source", "").lower()
    
    # Expanded Burnaby facilities list for robust city detection
    burnaby_indicators = [
        "burnaby", "bonsor", "edmonds", "shadbolt", "eileen dailly", "kensington", "metrotown", "brentwood", "deer lake",
        "christine sinclair", "temporary cameron", "barnet marine", "riverway", "rosemary brown", "confederation"
    ]
    # Expanded New Westminster facilities list for robust city detection
    new_west_indicators = [
        "new westminster", "təməsew̓txʷ", "queensborough", "queen's park", "moody park", "sapperton", "leisure & lap pool", "centennial community centre", "queensborough community centre", "queen's park arena", "moody park arena", "canada games pool", "fridge", "gymnasium", "studio", "multipurpose room", "activity room", "meeting room", "weight room", "fitness studio", "yoga studio", "dance studio", "arts studio", "pottery studio", "music room", "stage", "lobby", "lounge", "kitchen", "playground", "field", "park"
    ]
    if existing_city in ["burnaby", "new westminster"]:
        # Use the city already set by the scraper
        normalized["city"] = existing_city.title()
    elif "new west perfectmind" in source:
        normalized["city"] = "New Westminster"
    else:
        if any(indicator in facility for indicator in burnaby_indicators) or any(indicator in general_location for indicator in burnaby_indicators):
            normalized["city"] = "Burnaby"
        elif any(indicator in facility for indicator in new_west_indicators) or any(indicator in general_location for indicator in new_west_indicators):
            normalized["city"] = "New Westminster"
        else:
            normalized["city"] = "Unknown"
        
    # Use existing source if set, otherwise infer from city
    existing_source = activity.get("source", "")
    if existing_source and existing_source != "Unknown":
        normalized["source"] = existing_source
    else:
        # Infer source from city if not already set
        if normalized["city"] == "Burnaby":
            normalized["source"] = "Burnaby ActiveCommunities"
        elif normalized["city"] == "New Westminster":
            normalized["source"] = "New West PerfectMind"
        else:
            normalized["source"] = "Unknown"

    # Set facility and general_location fields robustly
    # For New West, 'location' is the true facility name
    if activity.get("location"):
        normalized["facility"] = activity["location"]
    elif activity.get("facility"):
        normalized["facility"] = activity["facility"]
    elif activity.get("general_location"):
        normalized["facility"] = activity["general_location"]
    else:
        normalized["facility"] = "Unknown"

    if activity.get("general_location"):
        normalized["general_location"] = activity["general_location"]
    else:
        normalized["general_location"] = normalized["facility"]

    return normalized 