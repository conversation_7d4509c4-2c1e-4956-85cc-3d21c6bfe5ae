#!/usr/bin/env python3
"""
Debug script to investigate Burnaby activities in Qdrant database.
"""

import asyncio
import logging
from qdrant_client import AsyncQdrantClient, models
from multi_tool_agent.config import AgentConfig
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_burnaby_data():
    """Query Qdrant for Burnaby activities and log details to investigate data discrepancy."""
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        logger.info("Connected to Qdrant client.")

        # Create filters for Burnaby activities using the newly indexed field
        burnaby_source_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="source",
                    match=models.MatchValue(value="Burnaby ActiveCommunities")
                )
            ]
        )
        # Query Qdrant for Burnaby activities using the indexed field
        logger.info("Querying with source filter for 'Burnaby ActiveCommunities' in source field...")
        source_results = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=[0.0] * 768,  # Dummy vector, we just want to filter
            query_filter=burnaby_source_filter,
            limit=100,
            with_payload=True,
        )
        logger.info(f"Found {len(source_results)} Burnaby activities with source filter.")
        if len(source_results) > 0:
            for idx, point in enumerate(source_results, 1):
                payload = point.payload
                source = payload.get("source", payload.get("metadata", {}).get("source", "N/A"))
                city = payload.get("city", payload.get("metadata", {}).get("city", "N/A"))
                min_age = payload.get("min_age_years", payload.get("metadata", {}).get("min_age_years", "N/A"))
                max_age = payload.get("max_age_years", payload.get("metadata", {}).get("max_age_years", "N/A"))
                name = payload.get("name", payload.get("metadata", {}).get("name", "N/A"))
                location = payload.get("general_location", payload.get("metadata", {}).get("general_location", "N/A"))

                logger.info(f"Activity {idx} (from source filter):")
                logger.info(f"  Name: {name}")
                logger.info(f"  Source: {source}")
                logger.info(f"  City: {city}")
                logger.info(f"  General Location: {location}")
                logger.info(f"  Min Age: {min_age}")
                logger.info(f"  Max Age: {max_age}")
        else:
            logger.warning("No activities found with source filter. The data might not match the expected 'Burnaby ActiveCommunities' value in source field.")

        logger.info("Querying without filter to inspect field values and full payload...")
        general_results = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=[0.0] * 768,  # Dummy vector, no filter to get any data
            limit=10,  # Smaller sample to log full payload without overload
            with_payload=True,
        )
        logger.info(f"Found {len(general_results)} activities in general query.")
        source_values = set()
        city_values = set()
        location_values = set()
        burnaby_related = 0
        for idx, point in enumerate(general_results, 1):
            payload = point.payload
            source = payload.get("source", "N/A")
            source_values.add(source)
            city = payload.get("city", payload.get("metadata", {}).get("city", "N/A"))
            city_values.add(str(city))
            name = payload.get("name", payload.get("metadata", {}).get("name", "N/A"))
            location = payload.get("general_location", payload.get("metadata", {}).get("general_location", "N/A"))
            location_values.add(str(location))

            logger.info(f"Activity {idx} (from general query):")
            logger.info(f"  Name: {name}")
            logger.info(f"  Source: {source}")
            logger.info(f"  City: {city}")
            logger.info(f"  General Location: {location}")
            # Check for Burnaby in any relevant field
            if any("burnaby" in str(val).lower() for val in [source, city, location, name]):
                burnaby_related += 1
                logger.info(f"  ** Burnaby-related content detected **")
            # Log full payload for detailed inspection
            logger.info(f"  Full Payload: {payload}")
        logger.info(f"Unique source values in sample: {source_values}")
        logger.info(f"Unique city values in sample: {city_values}")
        logger.info(f"Unique general location values in sample: {location_values}")
        logger.info(f"Total Burnaby-related activities in sample: {burnaby_related}")

        # Log details of the results from source filter as it might be more reliable
        burnaby_count = 0
        age_5_compatible = 0
        for idx, point in enumerate(source_results, 1):
            payload = point.payload
            source = payload.get("source", payload.get("metadata", {}).get("source", "N/A"))
            city = payload.get("city", payload.get("metadata", {}).get("city", "N/A"))
            min_age = payload.get("min_age_years", payload.get("metadata", {}).get("min_age_years", "N/A"))
            max_age = payload.get("max_age_years", payload.get("metadata", {}).get("max_age_years", "N/A"))
            name = payload.get("name", payload.get("metadata", {}).get("name", "N/A"))
            location = payload.get("general_location", payload.get("metadata", {}).get("general_location", "N/A"))

            logger.info(f"Activity {idx} (from source filter):")
            logger.info(f"  Name: {name}")
            logger.info(f"  Source: {source}")
            logger.info(f"  City: {city}")
            logger.info(f"  General Location: {location}")
            logger.info(f"  Min Age: {min_age}")
            logger.info(f"  Max Age: {max_age}")

            if "Burnaby" in str(source) or "Burnaby" in str(city) or "Burnaby" in str(location):
                burnaby_count += 1
                if isinstance(min_age, (int, float)) and min_age <= 5 and (max_age is None or isinstance(max_age, (int, float)) and max_age >= 5):
                    age_5_compatible += 1

        logger.info(f"Summary from source filter: {burnaby_count} Burnaby activities in sample, {age_5_compatible} compatible with age 5.")

        # Get total count of points in collection for reference
        total_points = await qdrant_client.count(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME
        )
        logger.info(f"Total points in collection: {total_points.count}")

        # Count Burnaby points using the indexed filter
        burnaby_source_points = await qdrant_client.count(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            count_filter=burnaby_source_filter
        )
        logger.info(f"Total Burnaby points with source filter: {burnaby_source_points.count}")

        # --- Date Consistency Analysis ---
        all_points = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=burnaby_source_filter,
            with_payload=True,
            limit=10000
        )
        missing_end = []
        same_start_end = []
        start_gt_end = []
        future_start = []
        now = datetime.utcnow().date()
        for pt in all_points[0]:
            p = pt.payload
            s = p.get("start_date")
            e = p.get("end_date")
            try:
                sd = datetime.strptime(s, "%Y-%m-%d").date() if s else None
                ed = datetime.strptime(e, "%Y-%m-%d").date() if e else None
            except Exception:
                sd = ed = None
            if not e:
                missing_end.append(p)
            if s and e and s == e:
                same_start_end.append(p)
            if sd and ed and sd > ed:
                start_gt_end.append(p)
            if sd and sd.year > 2026:
                future_start.append(p)
        logger.info(f"\n--- Date Consistency Summary ---")
        logger.info(f"Total Burnaby activities: {len(all_points[0])}")
        logger.info(f"Missing end_date: {len(missing_end)}")
        logger.info(f"Same start/end date: {len(same_start_end)}")
        logger.info(f"Start date > end date: {len(start_gt_end)}")
        logger.info(f"Start date in future (>2026): {len(future_start)}")
        logger.info(f"\nSample with missing end_date: {json.dumps(missing_end[:2], indent=2)}")
        logger.info(f"\nSample with same start/end: {json.dumps(same_start_end[:2], indent=2)}")
        logger.info(f"\nSample with start > end: {json.dumps(start_gt_end[:2], indent=2)}")
        logger.info(f"\nSample with future start: {json.dumps(future_start[:2], indent=2)}")

        # --- New Westminster Date Consistency Analysis ---
        newwest_source_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="source",
                    match=models.MatchValue(value="New West PerfectMind")
                )
            ]
        )
        all_nw_points = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=newwest_source_filter,
            with_payload=True,
            limit=10000
        )
        nw_missing_end = []
        nw_same_start_end = []
        nw_start_gt_end = []
        nw_future_start = []
        for pt in all_nw_points[0]:
            p = pt.payload
            s = p.get("start_date")
            e = p.get("end_date")
            try:
                sd = datetime.strptime(s, "%Y-%m-%d").date() if s else None
                ed = datetime.strptime(e, "%Y-%m-%d").date() if e else None
            except Exception:
                sd = ed = None
            if not e:
                nw_missing_end.append(p)
            if s and e and s == e:
                nw_same_start_end.append(p)
            if sd and ed and sd > ed:
                nw_start_gt_end.append(p)
            if sd and sd.year > 2026:
                nw_future_start.append(p)
        logger.info(f"\n--- New West Date Consistency Summary ---")
        logger.info(f"Total New West activities: {len(all_nw_points[0])}")
        logger.info(f"Missing end_date: {len(nw_missing_end)}")
        logger.info(f"Same start/end date: {len(nw_same_start_end)}")
        logger.info(f"Start date > end date: {len(nw_start_gt_end)}")
        logger.info(f"Start date in future (>2026): {len(nw_future_start)}")
        logger.info(f"\nSample with missing end_date: {json.dumps(nw_missing_end[:2], indent=2)}")
        logger.info(f"\nSample with same start/end: {json.dumps(nw_same_start_end[:2], indent=2)}")
        logger.info(f"\nSample with start > end: {json.dumps(nw_start_gt_end[:2], indent=2)}")
        logger.info(f"\nSample with future start: {json.dumps(nw_future_start[:2], indent=2)}")

    except Exception as e:
        logger.error(f"Error debugging Qdrant data: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(debug_burnaby_data())
