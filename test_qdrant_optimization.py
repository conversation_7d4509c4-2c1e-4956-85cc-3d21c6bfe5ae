#!/usr/bin/env python3
"""
Test script to verify Qdrant optimization fixes.
"""

import asyncio
import logging
from qdrant_client import AsyncQdrantClient, models
from multi_tool_agent.config import AgentConfig
from multi_tool_agent.unified_activity_search import create_qdrant_filter, normalize_activity

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_qdrant_optimization():
    """Test the optimized Qdrant setup and search functionality."""
    try:
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        logger.info("Connected to Qdrant client.")

        # Test 1: Verify HNSW index is enabled
        collection_info = await qdrant_client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        logger.info(f"Collection config: {collection_info.config}")
        
        # Check if HNSW is properly configured
        hnsw_config = collection_info.config.params.vectors.hnsw_config
        if hnsw_config and hnsw_config.m > 0:
            logger.info(f"✅ HNSW index is enabled with m={hnsw_config.m}")
            logger.info(f"   HNSW on disk: {getattr(hnsw_config, 'on_disk', False)}")
        else:
            logger.error("❌ HNSW index is disabled or misconfigured")
        
        # Check vector storage configuration
        vectors_config = collection_info.config.params.vectors
        logger.info(f"Vectors on disk: {vectors_config.on_disk}")
        quant_config = None
        if vectors_config.quantization_config:
            quant_config = vectors_config.quantization_config.scalar
            logger.info(f"Quantization: {quant_config.type}")
            logger.info(f"Quantized vectors in RAM: {getattr(quant_config, 'always_ram', False)}")
        
        # Determine optimization scenario
        if not vectors_config.on_disk and quant_config and getattr(quant_config, 'always_ram', False):
            scenario = "High Precision & High Speed (Scenario 3)"
        elif vectors_config.on_disk and quant_config and getattr(quant_config, 'always_ram', False):
            scenario = "High Speed & Low Memory (Scenario 1)"
        elif vectors_config.on_disk and hnsw_config and getattr(hnsw_config, 'on_disk', False):
            scenario = "High Precision & Low Memory (Scenario 2)"
        else:
            scenario = "Custom Configuration"
        
        logger.info(f"Detected optimization scenario: {scenario}")

        # Test 2: Test database-level filtering
        logger.info("\n=== Testing Database-Level Filtering ===")
        
        # Test Burnaby filter
        burnaby_filter = create_qdrant_filter({
            "location": ["burnaby"],
            "activities": [{"age": 5, "categories": ["Swimming"]}]
        })
        
        logger.info(f"Created filter: {burnaby_filter}")
        
        # Test search with filter
        search_results = await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=[0.0] * 768,  # Dummy vector
            query_filter=burnaby_filter,
            limit=10,
            with_payload=True,
        )
        
        logger.info(f"Found {len(search_results)} results with database filtering")
        
        # Test 3: Verify payload structure
        if search_results:
            sample_payload = search_results[0].payload
            logger.info(f"Sample payload structure: {list(sample_payload.keys())}")
            
            # Check for flat structure
            if "category" in sample_payload and "min_age_years" in sample_payload:
                logger.info("✅ Flat payload structure detected")
            else:
                logger.warning("⚠️ Payload may not be using flat structure")

        # Test 4: Performance test
        logger.info("\n=== Performance Test ===")
        import time
        
        # Test without filter (should be fast due to HNSW)
        start_time = time.time()
        await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=[0.0] * 768,
            limit=100,
            with_payload=True,
        )
        no_filter_time = time.time() - start_time
        
        # Test with filter (should be fast due to payload indexes)
        start_time = time.time()
        await qdrant_client.search(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query_vector=[0.0] * 768,
            query_filter=burnaby_filter,
            limit=100,
            with_payload=True,
        )
        with_filter_time = time.time() - start_time
        
        logger.info(f"Search without filter: {no_filter_time:.3f}s")
        logger.info(f"Search with filter: {with_filter_time:.3f}s")
        
        if no_filter_time < 1.0 and with_filter_time < 1.0:
            logger.info("✅ Search performance is good")
        else:
            logger.warning("⚠️ Search performance may need optimization")

        # Test 5: Verify payload indexes
        logger.info("\n=== Verifying Payload Indexes ===")
        try:
            # Test age range filtering
            age_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key="min_age_years",
                        range=models.Range(lte=5)
                    )
                ]
            )
            
            age_results = await qdrant_client.search(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                query_vector=[0.0] * 768,
                query_filter=age_filter,
                limit=5,
                with_payload=True,
            )
            logger.info(f"✅ Age filtering works: {len(age_results)} results")
            
        except Exception as e:
            logger.error(f"❌ Age filtering failed: {e}")

        await qdrant_client.close()
        logger.info("✅ All tests completed successfully!")

    except Exception as e:
        logger.error(f"Error during testing: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_qdrant_optimization()) 