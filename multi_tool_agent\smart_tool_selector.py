#!/usr/bin/env python3
"""
Smart Tool Selector for Multi-Source Activity Search

This module implements intelligent tool selection that:
1. Routes queries to the most appropriate data source
2. Handles fallback strategies when primary sources fail
3. Scales with new cities and data sources
4. Provides consistent user experience across all sources
"""

import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from .query_router import QueryRouter, QueryType, DataSource, RoutingDecision

logger = logging.getLogger(__name__)

@dataclass
class ToolResult:
    """Result from a tool execution."""
    success: bool
    data: Any
    source: DataSource
    message: str
    should_fallback: bool = False

class SmartToolSelector:
    """
    Intelligent tool selector that routes queries to appropriate tools
    and handles fallback strategies.
    """
    
    def __init__(self):
        self.router = QueryRouter()
        self.execution_stats = {
            'total_queries': 0,
            'successful_primary': 0,
            'required_fallback': 0,
            'failed_queries': 0
        }
    
    async def execute_query(
        self, 
        query: str, 
        tool_context: Any,
        available_tools: Dict[str, Callable]
    ) -> ToolResult:
        """
        Execute a query using the most appropriate tool with fallback strategy.
        
        Args:
            query: User's query
            tool_context: Tool execution context
            available_tools: Dictionary of available tool functions
            
        Returns:
            ToolResult with execution outcome
        """
        self.execution_stats['total_queries'] += 1
        
        # Get routing decision
        routing = self.router.route_query(query)
        logger.info(f"Query routing: {routing.query_type.value} -> {routing.primary_source.value} (confidence: {routing.confidence})")
        
        # Try primary source
        primary_result = await self._execute_with_source(
            query, routing.primary_source, routing, tool_context, available_tools
        )
        
        if primary_result.success and not primary_result.should_fallback:
            self.execution_stats['successful_primary'] += 1
            return primary_result
        
        # Try fallback sources
        for fallback_source in routing.fallback_sources:
            logger.info(f"Primary source failed/insufficient, trying fallback: {fallback_source.value}")
            fallback_result = await self._execute_with_source(
                query, fallback_source, routing, tool_context, available_tools
            )
            
            if fallback_result.success:
                self.execution_stats['required_fallback'] += 1
                return fallback_result
        
        # All sources failed
        self.execution_stats['failed_queries'] += 1
        return ToolResult(
            success=False,
            data=None,
            source=routing.primary_source,
            message=f"All data sources failed for query: {query}",
            should_fallback=False
        )
    
    async def _execute_with_source(
        self,
        query: str,
        source: DataSource,
        routing: RoutingDecision,
        tool_context: Any,
        available_tools: Dict[str, Callable]
    ) -> ToolResult:
        """Execute query with a specific data source."""
        
        try:
            if source == DataSource.LOCAL_DATABASE:
                return await self._execute_local_database_search(query, routing, tool_context, available_tools)
            elif source == DataSource.WEB_SEARCH:
                return await self._execute_web_search(query, routing, tool_context, available_tools)
            elif source == DataSource.MEMORY_SYSTEM:
                return await self._execute_memory_query(query, routing, tool_context, available_tools)
            else:
                return ToolResult(
                    success=False,
                    data=None,
                    source=source,
                    message=f"Unsupported data source: {source.value}"
                )
                
        except Exception as e:
            logger.error(f"Error executing query with {source.value}: {e}")
            return ToolResult(
                success=False,
                data=None,
                source=source,
                message=f"Execution error: {str(e)}"
            )
    
    async def _execute_local_database_search(
        self, query: str, routing: RoutingDecision, tool_context: Any, available_tools: Dict[str, Callable]
    ) -> ToolResult:
        """Execute search against local activity database."""
        
        # First extract filters
        if 'extract_activity_filters' in available_tools:
            try:
                filter_result = available_tools['extract_activity_filters'](query)
                if filter_result.get('status') != 'success':
                    return ToolResult(
                        success=False,
                        data=None,
                        source=DataSource.LOCAL_DATABASE,
                        message="Failed to extract activity filters"
                    )
                
                filters = filter_result.get('filters', {})
            except Exception as e:
                logger.error(f"Filter extraction failed: {e}")
                filters = {}
        else:
            filters = {}
        
        # Then search activities
        if 'get_activity_search' in available_tools:
            try:
                search_result = await available_tools['get_activity_search'](query, filters, tool_context)
                
                if search_result.get('status') == 'success':
                    results = search_result.get('results', [])
                    if results:
                        return ToolResult(
                            success=True,
                            data=search_result,
                            source=DataSource.LOCAL_DATABASE,
                            message=f"Found {len(results)} activities in local database"
                        )
                    else:
                        # No results found - should fallback to web search
                        return ToolResult(
                            success=False,
                            data=search_result,
                            source=DataSource.LOCAL_DATABASE,
                            message="No activities found in local database",
                            should_fallback=True
                        )
                else:
                    return ToolResult(
                        success=False,
                        data=search_result,
                        source=DataSource.LOCAL_DATABASE,
                        message=search_result.get('message', 'Local database search failed')
                    )
                    
            except Exception as e:
                logger.error(f"Activity search failed: {e}")
                return ToolResult(
                    success=False,
                    data=None,
                    source=DataSource.LOCAL_DATABASE,
                    message=f"Activity search error: {str(e)}"
                )
        else:
            return ToolResult(
                success=False,
                data=None,
                source=DataSource.LOCAL_DATABASE,
                message="Activity search tool not available"
            )
    
    async def _execute_web_search(
        self, query: str, routing: RoutingDecision, tool_context: Any, available_tools: Dict[str, Callable]
    ) -> ToolResult:
        """Execute web search."""
        
        if 'url_context_search' in available_tools:
            try:
                # For activity queries that fallback to web search, modify the query
                # to be more specific about finding local activities
                if routing.query_type == QueryType.ACTIVITY_SEARCH:
                    locations = routing.detected_entities.get('locations', [])
                    if locations:
                        enhanced_query = f"{query} in {' or '.join(locations)} British Columbia Canada"
                    else:
                        enhanced_query = f"{query} British Columbia Canada activities classes programs"
                else:
                    enhanced_query = query
                
                web_result = available_tools['url_context_search'](enhanced_query)
                
                if web_result.get('status') == 'success':
                    return ToolResult(
                        success=True,
                        data=web_result,
                        source=DataSource.WEB_SEARCH,
                        message="Found information via web search"
                    )
                else:
                    return ToolResult(
                        success=False,
                        data=web_result,
                        source=DataSource.WEB_SEARCH,
                        message=web_result.get('message', 'Web search failed')
                    )
                    
            except Exception as e:
                logger.error(f"Web search failed: {e}")
                return ToolResult(
                    success=False,
                    data=None,
                    source=DataSource.WEB_SEARCH,
                    message=f"Web search error: {str(e)}"
                )
        else:
            return ToolResult(
                success=False,
                data=None,
                source=DataSource.WEB_SEARCH,
                message="Web search tool not available"
            )
    
    async def _execute_memory_query(
        self, query: str, routing: RoutingDecision, tool_context: Any, available_tools: Dict[str, Callable]
    ) -> ToolResult:
        """Execute memory system query."""
        
        if 'retrieve_info_from_memory' in available_tools:
            try:
                memory_result = await available_tools['retrieve_info_from_memory'](query, tool_context)
                
                if memory_result.get('status') == 'success':
                    return ToolResult(
                        success=True,
                        data=memory_result,
                        source=DataSource.MEMORY_SYSTEM,
                        message="Retrieved information from memory"
                    )
                else:
                    return ToolResult(
                        success=False,
                        data=memory_result,
                        source=DataSource.MEMORY_SYSTEM,
                        message=memory_result.get('message', 'Memory query failed')
                    )
                    
            except Exception as e:
                logger.error(f"Memory query failed: {e}")
                return ToolResult(
                    success=False,
                    data=None,
                    source=DataSource.MEMORY_SYSTEM,
                    message=f"Memory query error: {str(e)}"
                )
        else:
            return ToolResult(
                success=False,
                data=None,
                source=DataSource.MEMORY_SYSTEM,
                message="Memory query tool not available"
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        total = self.execution_stats['total_queries']
        if total == 0:
            return self.execution_stats
        
        return {
            **self.execution_stats,
            'primary_success_rate': self.execution_stats['successful_primary'] / total,
            'fallback_rate': self.execution_stats['required_fallback'] / total,
            'failure_rate': self.execution_stats['failed_queries'] / total
        }

# Global instance
smart_tool_selector = SmartToolSelector()
