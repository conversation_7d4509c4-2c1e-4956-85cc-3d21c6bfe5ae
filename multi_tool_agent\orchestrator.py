import logging
from google.adk.agents import Agent
from google.adk.tools import agent_tool
from google.adk.planners import PlanReActPlanner
from google.adk.models.lite_llm import LiteLlm
from google.genai import types as genai_types

# Import specialist sub-agents
from .subagents import (
    activity_search_agent,
    memory_agent,
    quick_reference_agent,
    news_research_agent,
    search_agent,
)
# Import config for API keys
from .config import AgentConfig


# --- Define the Response Synthesizer Agent ---
# This agent uses a powerful model to craft user-facing responses.
response_synthesizer_agent = Agent(
    name="response_synthesizer_agent",
    model=AgentConfig.AGENT_MODEL,
    description="Synthesizes a final, user-friendly response based on a user's query and structured data from other tools.",
    instruction=(
        "You are an expert AI assistant creating helpful responses for parents. Your task is to take structured JSON data from a search tool and synthesize it into a perfect, user-facing response. Adhere strictly to the protocol.\n\n"
        "**DATA FORMATS:**\n"
        "1.  **Standard Search:** `[{\"name\": \"Art Class\", ...}]` (A list of single activities).\n"
        "2.  **Paired Search:** `[[{\"name\": \"Swim Class\"}, {\"name\": \"Art Class\"}]]` (A list of PAIRS of activities).\n\n"
        "**RESPONSE PROTOCOL:**\n"
        "1.  **Be Conversational:** Start with a friendly opening.\n"
        "2.  **Present Top Options:** Display the most relevant 3-5 options in a clear, bulleted list.\n"
        "3.  **MANDATORY DETAILS FOR EVERY ACTIVITY:** For every single activity you list, you MUST include:\n"
        "    - The specific **Venue** (e.g., 'Christine Sinclair Community Centre').\n"
        "    - The **Age Info** (e.g., 'Ages 3-5 yrs').\n"
        "    - Its **OWN UNIQUE BOOKING LINK**. The `url` field from the data MUST be formatted as a clickable markdown link, like `[Book Now](URL_from_data)`.\n\n"
        "--- CRITICAL: HOW TO HANDLE PAIRED RESULTS ---\n"
        "When the input data is a list of pairs (for 'back-to-back' requests), you MUST format each pair as a distinct group. **EACH CLASS WITHIN THE PAIR MUST HAVE ITS OWN SEPARATE BOOKING LINK.** Do not combine them.\n\n"
        "**EXAMPLE PAIRED INPUT DATA:**\n"
        '`[[{"name": "Swimming Lvl 1", "time": "11:00 AM", "venue": "Eileen Dailly Pool", "age_info": "3-5 yrs", "url": "https://example.com/swim_class_123"}, {"name": "Preschool Arts", "time": "11:30 AM", "venue": "Eileen Dailly Pool", "age_info": "3-5 yrs", "url": "https://example.com/art_class_456"}]]`\n\n'
        "**CORRECT PAIRED OUTPUT (This is the format you MUST follow):**\n"
        "I found a great back-to-back option for you at the **Eileen Dailly Pool**!\n\n"
        "**Option 1:**\n"
        "*   **11:00 AM - 11:25 AM:** Swimming Lvl 1 (Ages 3-5 yrs) - [Book This Class](https://example.com/swim_class_123)\n"
        "*   **11:30 AM - 11:55 AM:** Preschool Arts (Ages 3-5 yrs) - [Book This Class](https://example.com/art_class_456)\n\n"
        "This gives you a perfect combination of swimming and art, all at the same location!"
    ),
    tools=[]
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Wrap sub-agents as tools that the orchestrator can call
activity_search_tool = agent_tool.AgentTool(agent=activity_search_agent)
memory_management_tool = agent_tool.AgentTool(agent=memory_agent)
quick_reference_tool = agent_tool.AgentTool(agent=quick_reference_agent)
news_research_tool = agent_tool.AgentTool(agent=news_research_agent)
internet_search_tool = agent_tool.AgentTool(agent=search_agent)
synthesize_response_tool = agent_tool.AgentTool(agent=response_synthesizer_agent)

# --- Define the XLAM model for tool calling using LiteLLM ---
# Use a Groq model for fast tool calling
groq_model_for_tool_calling = LiteLlm(
    # Switched to Qwen3 32B model for enhanced tool calling
    model="groq/qwen/qwen3-32b",
)

# -----------------------------------------------------------------------------
# Lead Orchestrator Agent
# -----------------------------------------------------------------------------

OrchestratorAgent = Agent(
    name="multi_tool_agent",
    # Use the powerful Groq model for tool calling and reasoning
    model=groq_model_for_tool_calling,
    # Configure generation parameters as specified in the Chute AI example
    generate_content_config=genai_types.GenerateContentConfig(
        max_output_tokens=4096,  # Increased max tokens for Qwen3 32B
        temperature=0.6,         # Adjusted temperature for Qwen3 32B
        top_p=0.95,              # Added top_p for Qwen3 32B
    ),
    description="Lead agent that delegates user requests to specialist sub-agents.",
    instruction=(
        "You are a master orchestrator that intelligently uses a team of specialist agents to answer user queries. Your goal is to create a dynamic, multi-turn plan to fully resolve the user's request.\n\n"
        "**REASONING PROTOCOL:**\n"
        "1.  **Analyze and Plan:** First, analyze the user's request and create an initial plan. Your plan should be a sequence of steps to gather the necessary information.\n"
        "2.  **Execute and Reason:** Execute your plan one step at a time. After each tool call, observe the result under the `/*REASONING*/` tag. In your reasoning, you must assess if the information you have is sufficient to answer the user's query.\n"
        "3.  **Re-plan if Necessary:** If the information is insufficient, you MUST re-plan under the `/*REPLANNING*/` tag. Your new plan should involve using a different tool or the same tool with different parameters to get closer to the answer. For example, if the `activity_search_tool` yields no results, consider using the `internet_search_tool` to find general information about the activity.\n"
        "4.  **Synthesize Final Answer:** Once you have gathered sufficient information from one or more tool calls, your final action MUST be to call the `{synthesize_response_tool.name}`. Pass all the relevant information you have gathered to this tool to generate a comprehensive, user-facing response.\n\n"
        "**AVAILABLE SPECIALIST AGENTS (TOOLS):**\n"
        f"- `{activity_search_tool.name}`: Use this first for any queries about finding specific activities, classes, or camps.\n"
        f"- `{memory_management_tool.name}`: Use this to store or recall personal user information.\n"
        f"- `{internet_search_tool.name}`: Use this for general web searches, or if the activity search tool fails to find specific results.\n"
        f"- `{quick_reference_tool.name}`: Use for simple, direct questions like time or weather.\n\n"
        "Your ultimate goal is to be resourceful and persistent, using multiple tools in sequence if needed, until the user's query is fully resolved."
    ),
    planner=PlanReActPlanner(),
    tools=[
        activity_search_tool,
        memory_management_tool,
        quick_reference_tool,
        news_research_tool,
        internet_search_tool,
        synthesize_response_tool,
    ],
)

# Expose for Google ADK
root_agent = OrchestratorAgent

logger.info("Multi-agent orchestrator initialised as root_agent")
