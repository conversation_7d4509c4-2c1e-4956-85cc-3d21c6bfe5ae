#!/usr/bin/env python3

import json
from collections import defaultdict

def analyze_dates():
    """Analyze the current date parsing in the activities file."""
    
    print("🔍 ANALYZING DATE PARSING IN ACTIVITIES")
    print("=" * 50)
    
    # Load the activities file
    with open('multi_tool_agent/ingestion/fast_dropin_activities.json', 'r') as f:
        activities = json.load(f)
    
    print(f"Total activities: {len(activities)}")
    
    # Group by course_guid to see recurring activities
    by_course = defaultdict(list)
    for activity in activities:
        course_guid = activity.get('course_guid')
        if course_guid:
            by_course[course_guid].append(activity)
    
    print(f"Unique courses: {len(by_course)}")
    
    # Find courses with multiple occurrences
    recurring_courses = {guid: acts for guid, acts in by_course.items() if len(acts) > 1}
    print(f"Recurring courses: {len(recurring_courses)}")
    
    # Analyze a specific recurring course
    if recurring_courses:
        sample_guid = list(recurring_courses.keys())[0]
        sample_activities = recurring_courses[sample_guid]
        
        print(f"\n📋 SAMPLE RECURRING COURSE: {sample_guid}")
        print(f"Number of occurrences: {len(sample_activities)}")
        
        # Show first few occurrences
        for i, activity in enumerate(sample_activities[:5]):
            print(f"\nOccurrence {i+1}:")
            print(f"  Name: {activity.get('name')}")
            print(f"  Start Date: {activity.get('start_date')}")
            print(f"  End Date: {activity.get('end_date')}")
            print(f"  Days of Week: {activity.get('days_of_week')}")
            print(f"  Weekdays Mask: {activity.get('weekdays_mask')}")
            print(f"  Day of Week: {activity.get('day_of_week')}")
            
            # Check if original API fields are preserved
            if 'EventCurrentStartInJsFormat' in activity:
                print(f"  EventCurrentStartInJsFormat: {activity.get('EventCurrentStartInJsFormat')}")
            if 'EventDate' in activity:
                print(f"  EventDate: {activity.get('EventDate')}")
    
    # Check for date consistency issues
    print(f"\n🔍 DATE CONSISTENCY CHECK")
    same_dates = 0
    different_dates = 0
    
    for activities_list in by_course.values():
        if len(activities_list) > 1:
            dates = set(act.get('start_date') for act in activities_list)
            if len(dates) == 1:
                same_dates += 1
            else:
                different_dates += 1
    
    print(f"Courses with same dates for all occurrences: {same_dates}")
    print(f"Courses with different dates for occurrences: {different_dates}")
    
    if same_dates > 0:
        print("❌ PROBLEM: Some recurring courses have the same date for all occurrences")
    else:
        print("✅ GOOD: All recurring courses have different dates for each occurrence")

if __name__ == "__main__":
    analyze_dates()
