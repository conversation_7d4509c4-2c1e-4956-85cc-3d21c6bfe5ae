"""Activity-search related tool wrappers.
Currently delegates to existing `filter_extraction_tool.extract_activity_filters` for
backward compatibility so we can eventually deprecate the old module without
breaking imports.
"""

from __future__ import annotations

from typing import Dict, Any

from ..filter_extraction_tool import extract_activity_filters as _legacy_extract_activity_filters

__all__ = ["extract_activity_filters"]

def extract_activity_filters(query: str, tool_context=None) -> Dict[str, Any]:
    """Proxy to legacy `extract_activity_filters` tool.

    The ADK runtime automatically injects a `tool_context` positional argument
    when the function signature includes it.  To stay compatible with callers
    (LLM function calls supply only `query`) **and** with the ADK runtime, we
    accept `tool_context` as an optional second parameter and ignore it for
    now.  This prevents "missing required positional argument: tool_context"
    errors at runtime.
    """
    # Forward the (possibly None) tool_context to legacy implementation to
    # satisfy its required positional parameter.
    return _legacy_extract_activity_filters(query, tool_context) 