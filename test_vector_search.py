#!/usr/bin/env python3
"""
Test script to evaluate vector search results for a specific query.
"""

import asyncio
import logging
from multi_tool_agent.unified_activity_search import get_activity_search
from multi_tool_agent.config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_search_query():
    """Test vector search with a specific query."""
    query = "swimming classes for 5 year olds in Burnaby"
    filters = {
        "location": ["Burnaby"],
        "activities": [{"age": 5}]
    }
    tool_context = type('ToolContext', (), {'_qdrant_client': None})()

    logger.info(f"Testing vector search for query: '{query}' with filters: {filters}")
    result = await get_activity_search(query, filters, tool_context)
    
    if result["status"] == "success":
        logger.info(f"Search successful. Found {len(result['results'])} activities.")
        for idx, activity in enumerate(result["results"], 1):
            logger.info(f"Result {idx}:")
            logger.info(f"  Name: {activity.get('name', 'N/A')}")
            logger.info(f"  City: {activity.get('city', 'N/A')}")
            logger.info(f"  Age Info: {activity.get('age_info', 'N/A')}")
            logger.info(f"  Min Age: {activity.get('min_age_years', 'N/A')}")
            logger.info(f"  Max Age: {activity.get('max_age_years', 'N/A')}")
            logger.info(f"  Facility: {activity.get('facility', 'N/A')}")
            logger.info(f"  Start Time: {activity.get('start_time_iso', 'N/A')}")
            logger.info(f"  End Time: {activity.get('end_time_iso', 'N/A')}")
            logger.info(f"  Start Date: {activity.get('start_date', 'N/A')}")
            logger.info(f"  End Date: {activity.get('end_date', 'N/A')}")
    else:
        logger.error(f"Search failed: {result.get('message', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(test_search_query())
