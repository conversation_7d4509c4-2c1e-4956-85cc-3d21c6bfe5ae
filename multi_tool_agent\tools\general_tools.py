"""General purpose tools such as web search and time utilities."""

from __future__ import annotations

import logging
from datetime import datetime
from typing import Optional
from zoneinfo import ZoneInfo

import google.generativeai as genai
from google.genai import types

from ..config import AgentConfig
from .decorators import tool_safe

logger = logging.getLogger(__name__)

# Configure the generative AI client once
try:
    genai.configure(api_key=AgentConfig.GOOGLE_API_KEY)
except Exception as e:
    logger.warning("Failed to configure google genai client: %s", e)

# -----------------------------------------------------------------------------
# Public tool functions
# -----------------------------------------------------------------------------

def url_context_search(query: str, url: Optional[str] = None) -> dict:
    """Search the web or analyse a URL in order to answer a question."""
    logger.info("[general_tools] url_context_search query='%s' url=%s", query, url)
    try:
        client = genai.Client()
        tool_defs = [types.Tool(google_search=types.GoogleSearch())]
        if url:
            tool_defs.append(types.Tool(url_context=types.UrlContext()))

        content = f"{query} Based on: {url}" if url else query
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=content,
            config=types.GenerateContentConfig(tools=tool_defs),
        )

        return {
            "status": "success",
            "answer": getattr(response, "text", "No answer returned"),
        }
    except Exception as e:
        logger.error("url_context_search failed: %s", e, exc_info=True)
        return {"status": "error", "message": str(e)}


@tool_safe()
def get_current_time(timezone: str = "America/Vancouver") -> dict:
    """Return iso formatted current time in supplied timezone (defaults to BC)."""
    try:
        tz = ZoneInfo(timezone)
        now = datetime.now(tz)
        return {
            "status": "success",
            "datetime": now.isoformat(),
            "report": now.strftime("%Y-%m-%d %H:%M:%S %Z"),
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}


# -----------------------------------------------------------------------------
# Simple weather lookup using Open-Meteo API (no key required)
# -----------------------------------------------------------------------------

import json
from urllib import request as _urlrequest, parse as _urlparse


_CITY_COORDS = {
    # British Columbia major cities
    "new west": (49.2057, -122.9110),
    "new westminster": (49.2057, -122.9110),
    "vancouver": (49.2827, -123.1207),
    "victoria": (48.4284, -123.3656),
}


def _fetch_open_meteo(lat: float, lon: float) -> Optional[dict]:
    url = (
        "https://api.open-meteo.com/v1/forecast?latitude="
        f"{lat}&longitude={lon}&current_weather=true&temperature_unit=celsius"
    )
    try:
        with _urlrequest.urlopen(url, timeout=10) as resp:
            data = json.loads(resp.read().decode())
            return data.get("current_weather")
    except Exception as e:
        logger.warning("Open-Meteo request failed: %s", e)
        return None


@tool_safe()
def get_weather(city: str) -> dict:
    """Get today's current weather for a major city in British Columbia.

    Args:
        city: Name of the city (e.g., "Vancouver", "New West", "Victoria").

    Returns:
        dict with keys:
            status: "success" or "error"
            report: human-readable weather summary when successful
            raw: full JSON from Open-Meteo (only on success)
            message: error description when status == "error"
    """
    loc_key = city.lower().strip()
    coords = None
    for key in _CITY_COORDS:
        if key in loc_key:
            coords = _CITY_COORDS[key]
            break

    if not coords:
        return {
            "status": "error",
            "message": "Location not supported. Try Vancouver, Victoria, or New West.",
        }

    weather = _fetch_open_meteo(*coords)
    if not weather:
        return {"status": "error", "message": "Failed to fetch weather data."}

    report = (
        f"{weather['temperature']}°C, wind {weather['windspeed']} km/h, "
        f"condition code {weather['weathercode']}"
    )
    return {"status": "success", "report": report, "raw": weather}


__all__ = ["url_context_search", "get_current_time", "get_weather"] 