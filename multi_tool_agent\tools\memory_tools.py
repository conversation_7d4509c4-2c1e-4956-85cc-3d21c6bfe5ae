"""Memory-related tools powered by Graphiti.
This module is self-contained so sub-agents can import memory operations
without depending on the legacy monolithic `agent.py` implementation.
"""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional

from google.adk.tools.tool_context import ToolContext
from .decorators import tool_safe

# logger must be defined early
logger = logging.getLogger(__name__)

from ..config import AgentConfig

# Optional Graphiti imports — wrap in try-except so absence doesn't crash app
try:
    from graphiti_core import Graphiti
    from graphiti_core.nodes import EpisodeType
    from graphiti_core.llm_client.gemini_client import (
        GeminiClient,
        LLMConfig as GeminiLLMConfig,
    )
    from graphiti_core.embedder.gemini import (
        GeminiEmbedder,
        GeminiEmbedderConfig,
    )
    from graphiti_core.search.search_config_recipes import (
        EDGE_HYBRID_SEARCH_RRF,
        NODE_HYBRID_SEARCH_RRF,
    )
    _GRAPHITI_AVAILABLE = True
except ModuleNotFoundError as err:
    logger.warning("Graphiti not available: %s", err)
    _GRAPHITI_AVAILABLE = False

# -----------------------------------------------------------------------------
# Graphiti client (only if library is present)
# -----------------------------------------------------------------------------

graphiti_client: Optional["Graphiti"] = None

if _GRAPHITI_AVAILABLE:
    try:
        graphiti_client = Graphiti(
            AgentConfig.NEO4J_URI,
            AgentConfig.NEO4J_USER,
            AgentConfig.NEO4J_PASSWORD,
            llm_client=GeminiClient(
                config=GeminiLLMConfig(
                    api_key=AgentConfig.GOOGLE_API_KEY,
                    model=AgentConfig.GRAPHITI_MODEL,
                )
            ),
            embedder=GeminiEmbedder(
                config=GeminiEmbedderConfig(
                    api_key=AgentConfig.GOOGLE_API_KEY,
                    embedding_model=AgentConfig.EMBEDDING_MODEL,
                )
            ),
            cross_encoder=None,
        )
        logger.info("Graphiti client initialised for memory tools")
    except Exception as e:
        logger.error("Unable to initialise Graphiti client: %s", e, exc_info=True)
        graphiti_client = None

# -----------------------------------------------------------------------------
# Public tool functions expected to be registered with ADK
# -----------------------------------------------------------------------------

@tool_safe()
async def retrieve_info_from_memory(query: str, tool_context: ToolContext) -> dict:
    """Retrieve relevant personal context from the Graphiti memory graph."""
    user_id = tool_context._invocation_context.user_id
    if not graphiti_client:
        return {"status": "error", "message": "Memory system is unavailable."}

    logger.info("[Memory] retrieving for user %s query='%s'", user_id, query)
    search_groups = [user_id, "bc_local_user"]

    # Parallel search for edges and nodes.
    edge_task = graphiti_client._search(
        query=query, config=EDGE_HYBRID_SEARCH_RRF, group_ids=search_groups
    )
    node_task = graphiti_client._search(
        query=query, config=NODE_HYBRID_SEARCH_RRF, group_ids=search_groups
    )

    results = await asyncio.gather(edge_task, node_task, return_exceptions=True)

    facts: list[str] = []
    # Edge results
    if not isinstance(results[0], Exception) and hasattr(results[0], "edges"):
        facts.extend([edge.fact.strip() for edge in results[0].edges if edge.fact])
    # Node results
    if not isinstance(results[1], Exception) and hasattr(results[1], "nodes"):
        for node in results[1].nodes:
            if text := getattr(node, "name", "").strip():
                facts.append(text)

    unique_facts = sorted(set(facts), key=len, reverse=True)
    return {
        "status": "success",
        "retrieved_information": "\n".join(unique_facts[:15]) if unique_facts else "No personal information found.",
        "count": len(unique_facts[:15]),
    }


@tool_safe()
async def store_simple_fact(fact: str, tool_context: ToolContext) -> dict:
    """Store a short fact about the user or their family."""
    if not fact or len(fact.strip()) < 3:
        return {"status": "error", "message": "Fact too short"}

    if not graphiti_client:
        return {"status": "error", "message": "Memory system not available"}

    user_id = tool_context._invocation_context.user_id
    cleaned = fact.strip()
    if not cleaned.endswith('.'):
        cleaned += '.'

    episode_name = f"fact_{user_id}_{int(datetime.now(timezone.utc).timestamp())}"
    await graphiti_client.add_episode(
        name=episode_name,
        episode_body=cleaned,
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        group_id=user_id,
    )
    logger.info("[Memory] stored fact for user %s: %s", user_id, cleaned)
    return {"status": "success", "message": "Fact stored."}


@tool_safe()
async def store_activity_preference(
    child_name: str,
    activity_type: str,
    preference_level: str,
    tool_context: ToolContext,
) -> dict:
    """Store a child's preference for a particular activity type."""
    if not graphiti_client:
        return {"status": "error", "message": "Memory system not available"}

    user_id = tool_context._invocation_context.user_id
    preference_fact = f"{child_name} {preference_level} {activity_type} activities."

    episode_name = (
        f"pref_{child_name}_{activity_type}_{int(datetime.now(timezone.utc).timestamp())}"
    )
    episode_body = (
        "Child Activity Preference:\n"
        f"EntityType: Preference\nvalue: {activity_type}\nstrength: {preference_level}\nChild: {child_name}"
    )

    await graphiti_client.add_episode(
        name=episode_name,
        episode_body=episode_body,
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        group_id=user_id,
    )
    logger.info("[Memory] stored preference for user %s child %s", user_id, child_name)
    return {"status": "success", "message": "Preference stored."}


@tool_safe()
async def store_registration_info(
    child_name: str,
    activity_name: str,
    provider: str,
    status: str,
    year: int,
    tool_context: ToolContext,
) -> dict:
    """Store a registration record for a child's activity."""
    if not graphiti_client:
        return {"status": "error", "message": "Memory system not available"}

    user_id = tool_context._invocation_context.user_id

    reg_fact = f"{child_name} {status} for {activity_name} with {provider} in {year}."

    episode_name = (
        f"reg_{child_name}_{activity_name}_{int(datetime.now(timezone.utc).timestamp())}"
    )
    episode_body = (
        "Registration Record:\n"
        f"EntityType: Registration\nyear: {year}\nstatus: {status}\nChild: {child_name}\nActivity: {activity_name}\nProvider: {provider}"
    )

    await graphiti_client.add_episode(
        name=episode_name,
        episode_body=episode_body,
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        group_id=user_id,
    )
    logger.info("[Memory] stored registration for user %s child %s", user_id, child_name)
    return {"status": "success", "message": "Registration info stored."}


# Fallback EpisodeType stub when Graphiti is unavailable so the rest of the
# module can still import successfully, albeit in no-op mode.

if not _GRAPHITI_AVAILABLE:
    class EpisodeType:
        text = "text"

__all__ = [
    "retrieve_info_from_memory",
    "store_simple_fact",
    "store_activity_preference",
    "store_registration_info",
] 