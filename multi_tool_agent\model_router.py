"""
Smart model routing for cost optimization.
Automatically selects the most cost-effective model for each task.
Can reduce costs by 40-60% through intelligent model selection.
"""

import logging
from typing import Dict, Optional
from enum import Enum

logger = logging.getLogger(__name__)

class TaskComplexity(Enum):
    """Task complexity levels for model selection."""
    SIMPLE = "simple"        # Basic queries, greetings, simple facts
    MEDIUM = "medium"        # Memory extraction, search queries
    COMPLEX = "complex"      # Analysis, reasoning, complex synthesis
    CRITICAL = "critical"    # Important decisions, safety-critical tasks

class TaskType(Enum):
    """Types of tasks for model routing."""
    MEMORY_EXTRACTION = "memory_extraction"
    SEARCH = "search"
    CONVERSATION = "conversation"
    ANALYSIS = "analysis"
    TOOL_USAGE = "tool_usage"
    ERROR_HANDLING = "error_handling"

class ModelRouter:
    """Intelligent model routing for cost optimization."""
    
    # Model costs per 1M tokens (approximate, as of 2024)
    MODEL_COSTS = {
        "gemini-2.5-flash-lite-preview-06-17": 0.35,  # Latest cost-effective model with thinking
    }
    
    # Model thinking capabilities
    MODEL_THINKING_SUPPORT = {
        "gemini-2.5-flash-lite-preview-06-17": True,
    }
    
    # Thinking token budgets per model and task complexity
    THINKING_BUDGETS = {
        "gemini-2.5-flash-lite-preview-06-17": {
            TaskComplexity.SIMPLE: 192,
            TaskComplexity.MEDIUM: 384,
            TaskComplexity.COMPLEX: 512,
            TaskComplexity.CRITICAL: 768
        }
    }
    
    def __init__(self):
        self.usage_stats = {
            "total_requests": 0,
            "cost_savings": 0.0,
            "model_usage": {}
        }
    
    def analyze_complexity(self, user_input: str, context: Optional[dict] = None) -> TaskComplexity:
        """Analyze task complexity based on input and context."""
        input_lower = user_input.lower()
        
        # Simple greetings and basic queries
        simple_patterns = [
            "hi", "hello", "thanks", "thank you", "yes", "no", "ok", "okay",
            "what time", "weather in", "my name is"
        ]
        if any(pattern in input_lower for pattern in simple_patterns):
            return TaskComplexity.SIMPLE
        
        # Complex analysis keywords
        complex_patterns = [
            "analyze", "compare", "explain why", "what if", "reasoning",
            "because", "relationship between", "implications"
        ]
        if any(pattern in input_lower for pattern in complex_patterns):
            return TaskComplexity.COMPLEX
        
        # Length-based complexity
        if len(user_input.split()) > 50:
            return TaskComplexity.COMPLEX
        elif len(user_input.split()) > 20:
            return TaskComplexity.MEDIUM
        
        return TaskComplexity.MEDIUM  # Default
    
    def detect_task_type(self, user_input: str, context: Optional[dict] = None) -> TaskType:
        """Detect the type of task based on input and context."""
        input_lower = user_input.lower()
        
        # Memory-related queries
        if any(word in input_lower for word in ["remember", "my name", "told you", "previous"]):
            return TaskType.MEMORY_EXTRACTION
        
        # Search queries
        if any(word in input_lower for word in ["search", "find", "look up", "what is", "tell me about"]):
            return TaskType.SEARCH
        
        # Tool usage
        if any(word in input_lower for word in ["time", "weather", "current"]):
            return TaskType.TOOL_USAGE
        
        return TaskType.CONVERSATION  # Default
    
    def select_model(self, user_input: str, context: Optional[dict] = None) -> str:
        """Select the optimal model for the given task."""
        task_type = self.detect_task_type(user_input, context)
        complexity = self.analyze_complexity(user_input, context)
        
        # Standardize on the latest cost-effective model
        model = "gemini-2.5-flash-lite-preview-06-17"
        
        # Log usage for analytics
        self.usage_stats["total_requests"] += 1
        self.usage_stats["model_usage"][model] = self.usage_stats["model_usage"].get(model, 0) + 1
        
        logger.info(f"Selected {model} for {task_type.value} task (complexity: {complexity.value})")
        return model
    
    def get_model_for_memory_extraction(self, conversation_complexity: float = 0.5) -> str:
        """Specialized method for memory extraction tasks."""
        # Standardize on the latest cost-effective model
        return "gemini-2.5-flash-lite-preview-06-17"
    
    def get_usage_stats(self) -> dict:
        """Return usage statistics and cost savings."""
        return {
            **self.usage_stats,
            "cost_savings": "N/A (standardized on single model)",
            "total_cost_savings_percentage": 0
        }

    def get_thinking_config(self, model: str, complexity: TaskComplexity) -> dict:
        """Get appropriate thinking configuration for the selected model and task."""
        if not self.MODEL_THINKING_SUPPORT.get(model, False):
            return {}
        
        thinking_budget = self.THINKING_BUDGETS.get(model, {}).get(
            complexity, 
            384  # Default budget
        )
        
        return {
            "thinking_budget": thinking_budget,
            "include_thoughts": True
        }
    
    def select_model_with_thinking(self, user_input: str, context: Optional[dict] = None) -> tuple[str, dict]:
        """Select optimal model and return both model name and thinking config."""
        task_type = self.detect_task_type(user_input, context)
        complexity = self.analyze_complexity(user_input, context)
        
        # Standardize on the latest cost-effective model
        model = "gemini-2.5-flash-lite-preview-06-17"
        
        # Get thinking config for selected model
        thinking_config = self.get_thinking_config(model, complexity)
        
        # Log usage for analytics
        self.usage_stats["total_requests"] += 1
        self.usage_stats["model_usage"][model] = self.usage_stats["model_usage"].get(model, 0) + 1
        
        logger.info(f"Selected {model} for {task_type.value} task (complexity: {complexity.value}) with thinking budget: {thinking_config.get('thinking_budget', 'N/A')}")
        return model, thinking_config

# Global router instance
model_router = ModelRouter() 