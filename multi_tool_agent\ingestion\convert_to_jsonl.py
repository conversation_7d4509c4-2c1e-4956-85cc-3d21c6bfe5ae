#!/usr/bin/env python3

"""
Convert the fast_dropin_activities.json file to the community_activities.jsonl format
required by the update_qdrant_activities.py script.
"""

import json
import os
from pathlib import Path

def convert_json_to_jsonl():
    """Convert the JSON file to JSONL format."""
    
    # Get the current directory
    current_dir = Path(__file__).parent
    
    # Define input and output paths
    input_file = current_dir / "fast_dropin_activities.json"
    output_file = Path(os.path.dirname(os.path.dirname(current_dir))) / "community_activities.jsonl"
    
    print(f"Converting {input_file} to {output_file}...")
    
    # Read the JSON file
    with open(input_file, "r", encoding="utf-8") as f:
        activities = json.load(f)
    
    print(f"Read {len(activities)} activities from {input_file}")
    
    # Write to JSONL file
    with open(output_file, "w", encoding="utf-8") as f:
        for activity in activities:
            # Add any additional fields required by the update_qdrant_activities.py script
            # For example, ensure all required fields are present
            if "record_id" not in activity:
                continue
                
            # Write the activity as a JSON line
            f.write(json.dumps(activity) + "\n")
    
    print(f"Successfully wrote {len(activities)} activities to {output_file}")
    print(f"You can now run 'python update_qdrant_activities.py' to update the Qdrant database.")

if __name__ == "__main__":
    convert_json_to_jsonl() 