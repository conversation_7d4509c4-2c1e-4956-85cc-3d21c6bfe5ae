#!/usr/bin/env python3

import asyncio
import logging
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multi_tool_agent.unified_activity_search import get_activity_search, _normalize_payload
from multi_tool_agent.filter_extraction_tool import extract_activity_filters

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

class MockToolContext:
    """Mock tool context for testing"""
    pass

async def debug_burnaby_swimming():
    """Debug the specific Burnaby swimming query that's failing"""
    
    print("🏊 DEBUGGING BURNABY SWIMMING QUERY")
    print("=" * 50)
    
    # Test the exact query that's failing
    query = "swimming classes for 5 year olds in Burnaby"
    
    print(f"Query: '{query}'")
    print()
    
    # Step 1: Extract filters
    print("1. EXTRACTING FILTERS")
    print("-" * 20)
    mock_context = MockToolContext()
    
    try:
        # We need to call this with tool_context, but let's see what the function signature is
        from multi_tool_agent.filter_extraction_tool import extract_activity_filters
        
        # Check if we can call it directly
        filters = {"query": query}  # Fallback if we can't call the function
        print(f"   Filters (fallback): {filters}")
        
    except Exception as e:
        print(f"   Error extracting filters: {e}")
        # Create manual filters for testing
        filters = {
            'location': ['Burnaby'],
            'activities': [{'categories': ['swimming'], 'age': 5}],
            'relationships': None,
            'day_of_week': [],
            'time_of_day': [],
            'price_constraints': {},
            'special_requirements': ''
        }
        print(f"   Using manual filters: {filters}")
    
    print()
    
    # Step 2: Search with filters
    print("2. SEARCHING WITH FILTERS")
    print("-" * 25)
    
    try:
        result = await get_activity_search(query, mock_context, filters)
        
        print(f"   Status: {result.get('status')}")
        print(f"   Search type: {result.get('search_type')}")
        print(f"   Number of results: {len(result.get('results', []))}")
        print()
        
        # Analyze the results
        if result.get('results'):
            print("3. ANALYZING RESULTS")
            print("-" * 20)
            
            for i, activity in enumerate(result['results'][:3]):  # Check first 3
                print(f"   Result {i+1}:")
                print(f"     Name: {activity.get('name', 'Unknown')}")
                print(f"     Venue: {activity.get('venue', 'Unknown')}")
                
                # Check the raw payload for source information
                raw_payload = activity.get('raw_payload', {})
                source = raw_payload.get('source', 'Unknown')
                location = raw_payload.get('location', 'Unknown')
                general_location = raw_payload.get('general_location', 'Unknown')
                
                print(f"     Source: {source}")
                print(f"     Location: {location}")
                print(f"     General Location: {general_location}")
                
                # Check if this is actually a Burnaby result
                is_burnaby = (
                    'burnaby' in source.lower() or
                    'burnaby' in location.lower() or
                    'burnaby' in general_location.lower() or
                    any(term in activity.get('venue', '').lower() for term in ['shadbolt', 'edmonds', 'metrotown', 'brentwood'])
                )
                
                is_new_west = (
                    'new west' in source.lower() or
                    'təməsew̓txʷ' in activity.get('venue', '').lower() or
                    'new westminster' in location.lower()
                )
                
                print(f"     Is Burnaby: {is_burnaby}")
                print(f"     Is New West: {is_new_west}")
                
                if is_new_west:
                    print(f"     ❌ PROBLEM: This is a New Westminster result!")
                elif is_burnaby:
                    print(f"     ✅ Good: This is a Burnaby result")
                else:
                    print(f"     ❓ Unknown location")
                
                print()
        else:
            print("   No results found")
            
    except Exception as e:
        print(f"   Error during search: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_burnaby_swimming())
